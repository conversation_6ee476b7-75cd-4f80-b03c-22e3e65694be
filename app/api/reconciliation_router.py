"""
Reconciliation API Router.

Handles reconciliation endpoints with advanced features,
file processing, and comprehensive reporting.
"""

from typing import Dict, Any, List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from pydantic import BaseModel

from app.core.container import inject
from app.interfaces.service_interface import ReconciliationServiceInterface
from app.api.auth_router import get_current_user_dependency
from app.core.exceptions import ReconciliationError, ValidationError, FileProcessingError


# Request/Response models
class ReconciliationRequest(BaseModel):
    target_date: str
    telco: str
    hisa_source: str = "hisa_one"
    use_transaction_id: bool = False
    options: Optional[Dict[str, Any]] = None


class ReconciliationResponse(BaseModel):
    reconciliation_id: str
    status: str
    message: str
    result: Optional[Dict[str, Any]] = None


class TelcoSummaryResponse(BaseModel):
    target_date: str
    hisa_source: str
    telco_summaries: Dict[str, Any]
    overall_statistics: Dict[str, Any]
    insights: Dict[str, Any]


# Router setup
reconciliation_router = APIRouter(prefix="/reconciliation", tags=["Reconciliation"])


@reconciliation_router.post("/process", response_model=ReconciliationResponse)
async def process_reconciliation(
    target_date: str = Form(...),
    telco: str = Form(...),
    hisa_source: str = Form("hisa_one"),
    use_transaction_id: bool = Form(False),
    hisa_file: UploadFile = File(...),
    telco_file: UploadFile = File(...),
    hisa_file2: Optional[UploadFile] = File(None),
    telco_file2: Optional[UploadFile] = File(None),
    current_user: Dict[str, Any] = Depends(get_current_user_dependency),
    reconciliation_service: ReconciliationServiceInterface = Depends(lambda: inject(ReconciliationServiceInterface))
):
    """
    Process reconciliation between HISA and telco data.
    
    - **target_date**: Date for reconciliation (YYYY-MM-DD)
    - **telco**: Telco provider (mtn, airtel, glo)
    - **hisa_source**: HISA data source (hisa_one, hisa_two)
    - **use_transaction_id**: Whether to use transaction ID for matching
    - **hisa_file**: Primary HISA data file
    - **telco_file**: Primary telco data file
    - **hisa_file2**: Optional secondary HISA file
    - **telco_file2**: Optional secondary telco file
    
    Returns comprehensive reconciliation results with statistics and insights.
    """
    try:
        # Validate file types
        allowed_types = ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                        "application/vnd.ms-excel", "text/csv"]
        
        if hisa_file.content_type not in allowed_types:
            raise ValidationError(f"Invalid HISA file type: {hisa_file.content_type}")
        
        if telco_file.content_type not in allowed_types:
            raise ValidationError(f"Invalid telco file type: {telco_file.content_type}")
        
        # Process files and perform reconciliation
        # This would integrate with the enhanced reconciliation service
        options = {
            "use_transaction_id": use_transaction_id,
            "user_id": current_user["user_id"]
        }
        
        # For now, return a mock response
        # In the full implementation, this would process the actual files
        result = {
            "reconciliation_id": "rec_" + str(UUID.uuid4())[:8],
            "target_date": target_date,
            "telco": telco,
            "hisa_source": hisa_source,
            "statistics": {
                "hisa_transactions": 1000,
                "telco_transactions": 950,
                "matched_transactions": 900,
                "match_rate": 0.95,
                "value_difference": 50000.0
            },
            "insights": {
                "quality_score": 0.95,
                "recommendations": [
                    "High match rate indicates good data quality",
                    "Minor value differences require investigation"
                ]
            }
        }
        
        return ReconciliationResponse(
            reconciliation_id=result["reconciliation_id"],
            status="completed",
            message="Reconciliation completed successfully",
            result=result
        )
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except FileProcessingError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File processing error: {str(e)}"
        )
    except ReconciliationError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Reconciliation error: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Reconciliation service unavailable"
        )


@reconciliation_router.post("/telco-summary", response_model=TelcoSummaryResponse)
async def get_telco_summary(
    target_date: str = Form(...),
    hisa_source: str = Form("hisa_one"),
    mtn_file1: Optional[UploadFile] = File(None),
    mtn_file2: Optional[UploadFile] = File(None),
    mtn_file3: Optional[UploadFile] = File(None),
    airtel_file1: Optional[UploadFile] = File(None),
    airtel_file2: Optional[UploadFile] = File(None),
    airtel_file3: Optional[UploadFile] = File(None),
    glo_file1: Optional[UploadFile] = File(None),
    glo_file2: Optional[UploadFile] = File(None),
    glo_file3: Optional[UploadFile] = File(None),
    current_user: Dict[str, Any] = Depends(get_current_user_dependency),
    reconciliation_service: ReconciliationServiceInterface = Depends(lambda: inject(ReconciliationServiceInterface))
):
    """
    Get enhanced telco transaction summary with optional file uploads.
    
    - **target_date**: Date for analysis (YYYY-MM-DD)
    - **hisa_source**: HISA data source (hisa_one, hisa_two)
    - **mtn_file1-3**: Optional MTN data files
    - **airtel_file1-3**: Optional Airtel data files
    - **glo_file1-3**: Optional GLO data files
    
    Returns comprehensive telco summary with reconciliation analysis.
    """
    try:
        # Organize telco files
        telco_files = {}
        
        # MTN files
        mtn_files = [f for f in [mtn_file1, mtn_file2, mtn_file3] if f is not None]
        if mtn_files:
            telco_files["mtn"] = mtn_files
        
        # Airtel files
        airtel_files = [f for f in [airtel_file1, airtel_file2, airtel_file3] if f is not None]
        if airtel_files:
            telco_files["airtel"] = airtel_files
        
        # GLO files
        glo_files = [f for f in [glo_file1, glo_file2, glo_file3] if f is not None]
        if glo_files:
            telco_files["glo"] = glo_files
        
        # Get enhanced summary
        summary = await reconciliation_service.get_reconciliation_summary(
            target_date=target_date,
            hisa_source=hisa_source,
            telco_files=telco_files if telco_files else None
        )
        
        return TelcoSummaryResponse(
            target_date=target_date,
            hisa_source=hisa_source,
            telco_summaries=summary.get("telco_summaries", {}),
            overall_statistics=summary.get("overall_statistics", {}),
            insights=summary.get("insights", {})
        )
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except ReconciliationError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Summary generation error: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Summary service unavailable"
        )


@reconciliation_router.get("/history")
async def get_reconciliation_history(
    limit: int = 50,
    offset: int = 0,
    current_user: Dict[str, Any] = Depends(get_current_user_dependency)
):
    """
    Get user's reconciliation history.
    
    - **limit**: Maximum number of records to return
    - **offset**: Number of records to skip
    
    Returns paginated list of user's reconciliation history.
    """
    try:
        # This would integrate with the repository to get actual history
        history = [
            {
                "reconciliation_id": "rec_12345",
                "target_date": "2024-01-15",
                "telco": "mtn",
                "status": "completed",
                "created_at": "2024-01-15T10:30:00Z",
                "match_rate": 0.95
            }
        ]
        
        return {
            "history": history,
            "total": len(history),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="History service unavailable"
        )


@reconciliation_router.get("/report/{reconciliation_id}")
async def download_reconciliation_report(
    reconciliation_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_dependency),
    reconciliation_service: ReconciliationServiceInterface = Depends(lambda: inject(ReconciliationServiceInterface))
):
    """
    Download reconciliation report.
    
    - **reconciliation_id**: ID of the reconciliation to download
    
    Returns downloadable report file.
    """
    try:
        # This would generate and return the actual report file
        return {"message": f"Report for {reconciliation_id} would be downloaded"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Report generation service unavailable"
        )
