"""
API package for HISA Reconciliation Manager.

This package contains API routers and endpoints organized by domain,
following REST principles and clean architecture patterns.
"""

from .auth_router import auth_router
from .reconciliation_router import reconciliation_router
from .telco_router import telco_router
from .user_router import user_router
from .health_router import health_router

__all__ = [
    "auth_router",
    "reconciliation_router", 
    "telco_router",
    "user_router",
    "health_router",
]
