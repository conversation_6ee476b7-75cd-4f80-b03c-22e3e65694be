"""
Authentication API Router.

Handles authentication endpoints with proper error handling,
validation, and security following REST principles.
"""

from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Form
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel, EmailStr

from app.core.container import inject
from app.interfaces.service_interface import AuthenticationServiceInterface
from app.core.exceptions import AuthenticationError, ValidationError


# Request/Response models
class LoginResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str
    expires_in: int


class RefreshTokenRequest(BaseModel):
    refresh_token: str


class ChangePasswordRequest(BaseModel):
    current_password: str
    new_password: str


# Router setup
auth_router = APIRouter(prefix="/auth", tags=["Authentication"])


@auth_router.post("/login", response_model=LoginResponse)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    auth_service: AuthenticationServiceInterface = Depends(lambda: inject(AuthenticationServiceInterface))
):
    """
    Authenticate user and return JWT tokens.
    
    - **username**: User email address
    - **password**: User password
    
    Returns access token, refresh token, and token metadata.
    """
    try:
        # Authenticate user
        user_data = await auth_service.authenticate_user(
            form_data.username, 
            form_data.password
        )
        
        if not user_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Generate tokens
        tokens = await auth_service.generate_tokens(user_data)
        
        return LoginResponse(**tokens)
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service unavailable"
        )


@auth_router.post("/refresh", response_model=LoginResponse)
async def refresh_token(
    request: RefreshTokenRequest,
    auth_service: AuthenticationServiceInterface = Depends(lambda: inject(AuthenticationServiceInterface))
):
    """
    Refresh access token using valid refresh token.
    
    - **refresh_token**: Valid refresh token
    
    Returns new access token and refresh token.
    """
    try:
        tokens = await auth_service.refresh_token(request.refresh_token)
        return LoginResponse(**tokens)
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh service unavailable"
        )


@auth_router.post("/validate")
async def validate_token(
    token: str = Form(...),
    auth_service: AuthenticationServiceInterface = Depends(lambda: inject(AuthenticationServiceInterface))
):
    """
    Validate JWT token and return user information.
    
    - **token**: JWT access token to validate
    
    Returns user data if token is valid, otherwise returns error.
    """
    try:
        user_data = await auth_service.validate_token(token)
        
        if not user_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token"
            )
        
        return {"valid": True, "user": user_data}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token validation service unavailable"
        )


@auth_router.post("/logout")
async def logout():
    """
    Logout user (client-side token removal).
    
    Since JWT tokens are stateless, logout is handled client-side
    by removing the tokens from storage.
    """
    return {"message": "Logged out successfully"}


@auth_router.get("/me")
async def get_current_user(
    current_user: Dict[str, Any] = Depends(get_current_user_dependency)
):
    """
    Get current authenticated user information.
    
    Returns user profile data for the authenticated user.
    """
    return {"user": current_user}


# Dependency for getting current user
async def get_current_user_dependency(
    token: str = Depends(oauth2_scheme),
    auth_service: AuthenticationServiceInterface = Depends(lambda: inject(AuthenticationServiceInterface))
) -> Dict[str, Any]:
    """
    Dependency to get current authenticated user from JWT token.
    """
    try:
        user_data = await auth_service.validate_token(token)
        
        if not user_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return user_data
        
    except HTTPException:
        raise
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


# OAuth2 scheme for token extraction
from fastapi.security import OAuth2PasswordBearer
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/login")
