"""
Repository interfaces following Interface Segregation Principle.

These interfaces define contracts for data access operations,
enabling clean separation between business logic and data persistence.
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Generic, TypeVar
from uuid import UUID

T = TypeVar('T')


class BaseRepositoryInterface(ABC, Generic[T]):
    """Base repository interface for common CRUD operations."""
    
    @abstractmethod
    async def create(self, entity: T) -> T:
        """Create a new entity."""
        pass
    
    @abstractmethod
    async def get_by_id(self, entity_id: UUID) -> Optional[T]:
        """Get entity by ID."""
        pass
    
    @abstractmethod
    async def update(self, entity: T) -> T:
        """Update existing entity."""
        pass
    
    @abstractmethod
    async def delete(self, entity_id: UUID) -> bool:
        """Delete entity by ID."""
        pass
    
    @abstractmethod
    async def list_all(self, limit: int = 100, offset: int = 0) -> List[T]:
        """List all entities with pagination."""
        pass


class UserRepositoryInterface(BaseRepositoryInterface):
    """Interface for user-specific repository operations."""
    
    @abstractmethod
    async def get_by_email(self, email: str) -> Optional[Any]:
        """Get user by email address."""
        pass
    
    @abstractmethod
    async def get_active_users(self) -> List[Any]:
        """Get all active users."""
        pass
    
    @abstractmethod
    async def update_last_login(self, user_id: UUID) -> None:
        """Update user's last login timestamp."""
        pass


class TransactionRepositoryInterface(ABC):
    """Interface for transaction data operations."""
    
    @abstractmethod
    async def save_reconciliation_result(
        self, 
        result: Dict[str, Any],
        user_id: UUID
    ) -> UUID:
        """Save reconciliation result."""
        pass
    
    @abstractmethod
    async def get_reconciliation_history(
        self, 
        user_id: UUID,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Get user's reconciliation history."""
        pass
    
    @abstractmethod
    async def get_transaction_summary(
        self, 
        date: str,
        telco: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get transaction summary for date and telco."""
        pass


class FileRepositoryInterface(ABC):
    """Interface for file storage operations."""
    
    @abstractmethod
    async def store_file(
        self, 
        file_content: bytes,
        filename: str,
        file_type: str,
        user_id: UUID
    ) -> str:
        """Store file and return file path."""
        pass
    
    @abstractmethod
    async def get_file(self, file_path: str) -> Optional[bytes]:
        """Retrieve file content."""
        pass
    
    @abstractmethod
    async def delete_file(self, file_path: str) -> bool:
        """Delete file."""
        pass
    
    @abstractmethod
    async def list_user_files(
        self, 
        user_id: UUID,
        file_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """List files for user."""
        pass
