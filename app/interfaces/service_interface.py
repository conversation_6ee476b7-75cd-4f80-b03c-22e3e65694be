"""
Service interfaces following Interface Segregation Principle.

These interfaces define contracts for business logic operations,
enabling clean separation of concerns and testability.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from uuid import UUID
import pandas as pd
from fastapi import UploadFile


class AuthenticationServiceInterface(ABC):
    """Interface for authentication operations."""
    
    @abstractmethod
    async def authenticate_user(self, email: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate user credentials."""
        pass
    
    @abstractmethod
    async def generate_tokens(self, user_data: Dict[str, Any]) -> Dict[str, str]:
        """Generate access and refresh tokens."""
        pass
    
    @abstractmethod
    async def refresh_token(self, refresh_token: str) -> Dict[str, str]:
        """Refresh access token."""
        pass
    
    @abstractmethod
    async def validate_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Validate and decode token."""
        pass


class FileProcessingServiceInterface(ABC):
    """Interface for file processing operations."""
    
    @abstractmethod
    async def process_upload(
        self, 
        file: UploadFile,
        file_type: str,
        user_id: UUID
    ) -> Dict[str, Any]:
        """Process uploaded file."""
        pass
    
    @abstractmethod
    async def validate_file(self, file: UploadFile, expected_type: str) -> Dict[str, Any]:
        """Validate file format and content."""
        pass
    
    @abstractmethod
    async def convert_to_dataframe(self, file_path: str) -> pd.DataFrame:
        """Convert file to pandas DataFrame."""
        pass


class TelcoProcessingServiceInterface(ABC):
    """Interface for telco-specific processing operations."""
    
    @abstractmethod
    async def process_telco_data(
        self, 
        data: pd.DataFrame,
        telco: str
    ) -> pd.DataFrame:
        """Process telco-specific data transformations."""
        pass
    
    @abstractmethod
    async def validate_telco_data(
        self, 
        data: pd.DataFrame,
        telco: str
    ) -> Dict[str, Any]:
        """Validate telco data structure."""
        pass
    
    @abstractmethod
    async def calculate_telco_summary(
        self, 
        data: pd.DataFrame
    ) -> Dict[str, Any]:
        """Calculate telco transaction summary."""
        pass


class ReconciliationServiceInterface(ABC):
    """Interface for reconciliation operations."""
    
    @abstractmethod
    async def reconcile_transactions(
        self,
        hisa_data: pd.DataFrame,
        telco_data: pd.DataFrame,
        telco: str,
        target_date: str,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Perform transaction reconciliation."""
        pass
    
    @abstractmethod
    async def generate_reconciliation_report(
        self,
        reconciliation_result: Dict[str, Any],
        user_id: UUID
    ) -> str:
        """Generate reconciliation report file."""
        pass
    
    @abstractmethod
    async def get_reconciliation_summary(
        self,
        target_date: str,
        hisa_source: str,
        telco_files: Optional[Dict[str, List[UploadFile]]] = None
    ) -> Dict[str, Any]:
        """Get enhanced reconciliation summary."""
        pass


class NotificationServiceInterface(ABC):
    """Interface for notification operations."""
    
    @abstractmethod
    async def send_reconciliation_complete(
        self,
        user_id: UUID,
        result: Dict[str, Any]
    ) -> None:
        """Send reconciliation completion notification."""
        pass
    
    @abstractmethod
    async def send_error_notification(
        self,
        user_id: UUID,
        error: str,
        context: Dict[str, Any]
    ) -> None:
        """Send error notification."""
        pass


class AnalyticsServiceInterface(ABC):
    """Interface for analytics operations."""
    
    @abstractmethod
    async def track_user_activity(
        self,
        user_id: UUID,
        activity: str,
        metadata: Dict[str, Any]
    ) -> None:
        """Track user activity."""
        pass
    
    @abstractmethod
    async def get_usage_statistics(
        self,
        start_date: str,
        end_date: str
    ) -> Dict[str, Any]:
        """Get system usage statistics."""
        pass
    
    @abstractmethod
    async def get_user_consumption_summary(
        self,
        target_date: str,
        telco: str,
        hisa_source: str
    ) -> Dict[str, Any]:
        """Get user consumption summary."""
        pass
