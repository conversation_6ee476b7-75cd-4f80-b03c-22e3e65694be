"""
Enhanced Authentication Service Implementation.

Provides secure authentication with JWT tokens, refresh tokens,
and comprehensive security features following SOLID principles.
"""

from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from uuid import UUID
import jwt
import bcrypt
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, status

from app.interfaces.service_interface import AuthenticationServiceInterface
from app.interfaces.repository_interface import UserRepositoryInterface
from app.core.config import AppConfig
from app.core.exceptions import AuthenticationError, ValidationError


class EnhancedAuthService(AuthenticationServiceInterface):
    """
    Enhanced authentication service with advanced security features.
    
    Implements secure authentication, token management, and user session handling
    following security best practices and SOLID principles.
    """
    
    def __init__(
        self,
        user_repo: UserRepositoryInterface,
        config: AppConfig
    ):
        self._user_repo = user_repo
        self._config = config
        self._security_config = config.security
    
    async def authenticate_user(self, email: str, password: str) -> Optional[Dict[str, Any]]:
        """
        Authenticate user with enhanced security checks.
        """
        try:
            # Validate input
            if not email or not password:
                raise ValidationError("Email and password are required")
            
            # Get user from repository
            user = await self._user_repo.get_by_email(email.lower().strip())
            if not user:
                # Use constant-time comparison to prevent timing attacks
                bcrypt.checkpw(b"dummy", b"$2b$12$dummy.hash.to.prevent.timing.attacks")
                raise AuthenticationError("Invalid credentials")
            
            # Verify password
            if not self._verify_password(password, user.hashed_password):
                raise AuthenticationError("Invalid credentials")
            
            # Check if user is active
            if not getattr(user, 'is_active', True):
                raise AuthenticationError("Account is deactivated")
            
            # Update last login
            await self._user_repo.update_last_login(user.uid)
            
            # Return user data (excluding sensitive information)
            return {
                "user_id": str(user.uid),
                "email": user.email,
                "is_admin": getattr(user, 'is_admin', False),
                "last_login": datetime.utcnow().isoformat()
            }
            
        except (ValidationError, AuthenticationError):
            raise
        except Exception as e:
            raise AuthenticationError("Authentication failed") from e
    
    async def generate_tokens(self, user_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Generate secure JWT access and refresh tokens.
        """
        try:
            now = datetime.utcnow()
            user_id = user_data["user_id"]
            
            # Access token payload
            access_payload = {
                "sub": user_id,
                "email": user_data["email"],
                "is_admin": user_data.get("is_admin", False),
                "iat": now,
                "exp": now + timedelta(minutes=self._security_config.access_token_expire_minutes),
                "type": "access"
            }
            
            # Refresh token payload
            refresh_payload = {
                "sub": user_id,
                "iat": now,
                "exp": now + timedelta(days=self._security_config.refresh_token_expire_days),
                "type": "refresh"
            }
            
            # Generate tokens
            access_token = jwt.encode(
                access_payload,
                self._security_config.secret_key,
                algorithm=self._security_config.algorithm
            )
            
            refresh_token = jwt.encode(
                refresh_payload,
                self._security_config.secret_key,
                algorithm=self._security_config.algorithm
            )
            
            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "expires_in": self._security_config.access_token_expire_minutes * 60
            }
            
        except Exception as e:
            raise AuthenticationError("Failed to generate tokens") from e
    
    async def refresh_token(self, refresh_token: str) -> Dict[str, str]:
        """
        Refresh access token using valid refresh token.
        """
        try:
            # Decode and validate refresh token
            payload = jwt.decode(
                refresh_token,
                self._security_config.secret_key,
                algorithms=[self._security_config.algorithm]
            )
            
            # Verify token type
            if payload.get("type") != "refresh":
                raise AuthenticationError("Invalid token type")
            
            # Get user data
            user_id = payload["sub"]
            user = await self._user_repo.get_by_id(UUID(user_id))
            if not user:
                raise AuthenticationError("User not found")
            
            # Generate new tokens
            user_data = {
                "user_id": str(user.uid),
                "email": user.email,
                "is_admin": getattr(user, 'is_admin', False)
            }
            
            return await self.generate_tokens(user_data)
            
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Refresh token expired")
        except jwt.InvalidTokenError:
            raise AuthenticationError("Invalid refresh token")
        except Exception as e:
            raise AuthenticationError("Token refresh failed") from e
    
    async def validate_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        Validate and decode JWT token.
        """
        try:
            # Decode token
            payload = jwt.decode(
                token,
                self._security_config.secret_key,
                algorithms=[self._security_config.algorithm]
            )
            
            # Verify token type
            if payload.get("type") != "access":
                return None
            
            # Return user data from token
            return {
                "user_id": payload["sub"],
                "email": payload["email"],
                "is_admin": payload.get("is_admin", False),
                "exp": payload["exp"]
            }
            
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None
        except Exception:
            return None
    
    def _verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """
        Verify password using bcrypt with constant-time comparison.
        """
        try:
            return bcrypt.checkpw(
                plain_password.encode('utf-8'),
                hashed_password.encode('utf-8')
            )
        except Exception:
            return False
    
    def _hash_password(self, password: str) -> str:
        """
        Hash password using bcrypt with secure salt.
        """
        salt = bcrypt.gensalt(rounds=12)
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    async def change_password(
        self, 
        user_id: UUID, 
        current_password: str, 
        new_password: str
    ) -> bool:
        """
        Change user password with validation.
        """
        try:
            # Get user
            user = await self._user_repo.get_by_id(user_id)
            if not user:
                raise AuthenticationError("User not found")
            
            # Verify current password
            if not self._verify_password(current_password, user.hashed_password):
                raise AuthenticationError("Current password is incorrect")
            
            # Validate new password strength
            self._validate_password_strength(new_password)
            
            # Hash new password
            new_hashed = self._hash_password(new_password)
            
            # Update user password
            user.hashed_password = new_hashed
            await self._user_repo.update(user)
            
            return True
            
        except (AuthenticationError, ValidationError):
            raise
        except Exception as e:
            raise AuthenticationError("Password change failed") from e
    
    def _validate_password_strength(self, password: str) -> None:
        """
        Validate password strength requirements.
        """
        if len(password) < 8:
            raise ValidationError("Password must be at least 8 characters long")
        
        if not any(c.isupper() for c in password):
            raise ValidationError("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in password):
            raise ValidationError("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in password):
            raise ValidationError("Password must contain at least one digit")
        
        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            raise ValidationError("Password must contain at least one special character")
