"""
Enhanced HISA Reconciliation Manager Application.

Modern FastAPI application following SOLID principles with dependency injection,
clean architecture, and comprehensive error handling.
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
import logging
import time
from typing import Dict, Any

from app.core.config import get_config
from app.core.container import get_container
from app.core.exceptions import ApplicationError, ValidationError, AuthenticationError
from app.api import (
    auth_router,
    reconciliation_router,
    telco_router,
    user_router,
    health_router
)


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager for startup and shutdown events.
    """
    # Startup
    logger.info("Starting HISA Reconciliation Manager v2.0")
    
    # Initialize dependency injection container
    container = get_container()
    logger.info("Dependency injection container initialized")
    
    # Register services
    await register_services(container)
    logger.info("Services registered successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down HISA Reconciliation Manager v2.0")


async def register_services(container):
    """
    Register all application services with the DI container.
    """
    from app.services.enhanced_auth_service import EnhancedAuthService
    from app.services.enhanced_reconciliation_service import EnhancedReconciliationService
    from app.interfaces.service_interface import (
        AuthenticationServiceInterface,
        ReconciliationServiceInterface
    )
    
    # Register service implementations
    container.register_singleton(AuthenticationServiceInterface, EnhancedAuthService)
    container.register_singleton(ReconciliationServiceInterface, EnhancedReconciliationService)


def create_application() -> FastAPI:
    """
    Application factory following the Factory Pattern.
    """
    config = get_config()
    
    # Create FastAPI application
    app = FastAPI(
        title=config.api.title,
        version=config.api.version,
        description=config.api.description,
        lifespan=lifespan,
        docs_url="/docs" if config.debug else None,
        redoc_url="/redoc" if config.debug else None
    )
    
    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=config.api.cors_origins,
        allow_credentials=config.api.cors_credentials,
        allow_methods=config.api.cors_methods,
        allow_headers=config.api.cors_headers,
    )
    
    # Add custom middleware
    add_middleware(app)
    
    # Register routers
    register_routers(app)
    
    # Register exception handlers
    register_exception_handlers(app)
    
    # Mount static files
    if config.debug:
        app.mount("/static", StaticFiles(directory="static"), name="static")
    
    return app


def add_middleware(app: FastAPI):
    """
    Add custom middleware to the application.
    """
    
    @app.middleware("http")
    async def request_timing_middleware(request: Request, call_next):
        """Add request timing and logging."""
        start_time = time.time()
        
        # Log request
        logger.info(f"Request: {request.method} {request.url}")
        
        # Process request
        response = await call_next(request)
        
        # Calculate processing time
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        
        # Log response
        logger.info(f"Response: {response.status_code} - {process_time:.4f}s")
        
        return response
    
    @app.middleware("http")
    async def error_handling_middleware(request: Request, call_next):
        """Global error handling middleware."""
        try:
            return await call_next(request)
        except Exception as e:
            logger.error(f"Unhandled error: {str(e)}", exc_info=True)
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal server error",
                    "message": "An unexpected error occurred",
                    "request_id": getattr(request.state, "request_id", None)
                }
            )


def register_routers(app: FastAPI):
    """
    Register API routers with the application.
    """
    # API v1 routes (for backward compatibility)
    app.include_router(auth_router, prefix="/api/v1")
    app.include_router(reconciliation_router, prefix="/api/v1")
    app.include_router(telco_router, prefix="/api/v1")
    app.include_router(user_router, prefix="/api/v1")
    app.include_router(health_router, prefix="/api/v1")
    
    # API v2 routes (new enhanced endpoints)
    app.include_router(auth_router, prefix="/api/v2")
    app.include_router(reconciliation_router, prefix="/api/v2")
    app.include_router(telco_router, prefix="/api/v2")
    app.include_router(user_router, prefix="/api/v2")
    app.include_router(health_router, prefix="/api/v2")


def register_exception_handlers(app: FastAPI):
    """
    Register custom exception handlers.
    """
    
    @app.exception_handler(ValidationError)
    async def validation_error_handler(request: Request, exc: ValidationError):
        """Handle validation errors."""
        return JSONResponse(
            status_code=422,
            content={
                "error": "Validation Error",
                "message": exc.message,
                "field": getattr(exc, "field", None),
                "error_code": exc.error_code,
                "details": exc.details
            }
        )
    
    @app.exception_handler(AuthenticationError)
    async def authentication_error_handler(request: Request, exc: AuthenticationError):
        """Handle authentication errors."""
        return JSONResponse(
            status_code=401,
            content={
                "error": "Authentication Error",
                "message": exc.message,
                "error_code": exc.error_code
            },
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    @app.exception_handler(ApplicationError)
    async def application_error_handler(request: Request, exc: ApplicationError):
        """Handle general application errors."""
        return JSONResponse(
            status_code=500,
            content={
                "error": "Application Error",
                "message": exc.message,
                "error_code": exc.error_code,
                "details": exc.details
            }
        )
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """Handle HTTP exceptions with enhanced formatting."""
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": "HTTP Error",
                "message": exc.detail,
                "status_code": exc.status_code
            },
            headers=getattr(exc, "headers", None)
        )


# Create application instance
app = create_application()


# Health check endpoint
@app.get("/health")
async def health_check():
    """
    Health check endpoint for monitoring and load balancers.
    """
    config = get_config()
    return {
        "status": "healthy",
        "version": config.api.version,
        "environment": config.environment,
        "timestamp": time.time()
    }


# Root endpoint
@app.get("/")
async def root():
    """
    Root endpoint with API information.
    """
    config = get_config()
    return {
        "name": config.api.title,
        "version": config.api.version,
        "description": config.api.description,
        "docs_url": "/docs" if config.debug else None,
        "health_url": "/health"
    }


if __name__ == "__main__":
    import uvicorn
    
    config = get_config()
    uvicorn.run(
        "app.main_v2:app",
        host="0.0.0.0",
        port=8000,
        reload=config.debug,
        log_level=config.log_level.lower()
    )
