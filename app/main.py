import io
import os
from typing import Optional

from fastapi import (
    Depends,
    FastAPI,
    File,
    Form,
    HTTPException,
    UploadFile,
    status,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.staticfiles import StaticFiles
import pandas as pd
from sqlmodel import Session

from app import clean_merge_hisa, reconcile_logs
from app.airtel import prepare_airtel_telco
from app.auth import (
    authenticate_user,
    generate_tokens,
    get_current_user,
)
from app.database import get_session
from app.glo import prepare_glo_telco
from app.mtn import prepare_mtn_telco
from app.config import BASE_DIR
from app.managers import (
    HisaSFTPManager,
    HisaWalletManager,
)
from app.services.hisa_wallet_service import HisaWalletServiceFactory
from app.services.transaction_processor_service import TelcoTransactionProcessor
from app.utils import (
    get_telco_transaction_summary,
    get_telco_user_consumption_summary,
)
from app.utils import (
    compute_overall_balance,
    daterange_from_filter,
    extract_opening_closing,
    extract_user_from_wallets,
    sum_wallet_transactions,
)


app = FastAPI(
    title="Hisa Reconciliation Manager",
    max_request_size=100 * 1024 * 1024,  # 100MB limit
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Ensure the reports directory exists and mount it for static file serving.
REPORTS_DIR = os.path.join(BASE_DIR, "reports")
os.makedirs(REPORTS_DIR, exist_ok=True)
app.mount("/reports", StaticFiles(directory=REPORTS_DIR), name="reports")


@app.get("/")
def home():
    return {"message": "Welcome to Hisa Reconciliation Manager"}


@app.post("/login/", status_code=status.HTTP_200_OK)
def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    session: Session = Depends(get_session),
):
    user = authenticate_user(form_data.username, form_data.password, session)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    tokens = generate_tokens(data={"user": user.email, "user_id": str(user.uid)})
    return {"data": tokens}


@app.post("/reconcile_logs/")
async def reconciliation_manager(
    mno: str = Form(...),
    target_date: str = Form(...),
    hisa_file: UploadFile = File(...),
    hisa_file2: UploadFile = File(...),
    telco_file: UploadFile = File(...),
    telco_file2: UploadFile = File(None),
    use_transaction_id: bool = Form(False),
    user: dict = Depends(get_current_user),
):
    try:
        hisa_content = await hisa_file.read()
        hisa_dfs = [clean_merge_hisa(mno.upper(), "hisa1", io.BytesIO(hisa_content))]
        hisa_content2 = await hisa_file2.read()
        hisa_dfs.append(
            clean_merge_hisa(mno.upper(), "hisa2", io.BytesIO(hisa_content2))
        )
        hisa_df = pd.concat(hisa_dfs) if len(hisa_dfs) > 1 else hisa_dfs[0]

        # Process Telco files
        if mno.upper() == "MTN":
            mtn_content = await telco_file.read()
            mtn_dfs = [pd.read_excel(io.BytesIO(mtn_content))]

            if telco_file2:
                mtn_content2 = await telco_file2.read()
                mtn_dfs.append(pd.read_excel(io.BytesIO(mtn_content2)))

            mtn_df = pd.concat(mtn_dfs) if len(mtn_dfs) > 1 else mtn_dfs[0]
            cleaned_telco = prepare_mtn_telco(mtn_df)
            result = reconcile_logs(
                mno=mno.upper(),
                target_date=target_date,
                hisa_admin=hisa_df,
                telco_df=cleaned_telco,
                use_transaction_id=use_transaction_id,
            )
            response_data = {
                "hisa_all_count": result["hisa_all_count"],
                "hisa_all_value": result["hisa_all_value"],
                "telco_all_count": result["telco_all_count"],
                "telco_all_value": result["telco_all_value"],
                "hisa_success_count": result["hisa_success_count"],
                "telco_success_count": result["telco_success_count"],
                "matching_statistics": result["matching_statistics"],
                "hisa_total": result["hisa_total"],
                "telco_total": result["telco_total"],
                "difference": result["difference"],
                "missing_in_hisa_count": len(result["missing_in_hisa"]),
                "extra_in_hisa_count": len(result["extra_in_hisa"]),
                "local_duplicates_count": len(result["local_duplicates"]),
                "telco_duplicates_count": len(result["telco_duplicates"]),
                "duplicates_file_url": f"/reports/{mno.upper()}_reconciliation/duplicates_report/{os.path.basename(result['duplicates_file'])}",
                "missing_file_url": f"/reports/{mno.upper()}_reconciliation/missing_report/{os.path.basename(result['missing_file'])}",
                "user_consumption_file_url": f"/reports/{mno.upper()}_reconciliation/user_consumption_report/{os.path.basename(result['user_consumption_file'])}",
                # New fields
                "hisa_airtime_count": result["hisa_airtime_count"],
                "hisa_airtime_value": result["hisa_airtime_value"],
                "hisa_data_count": result["hisa_data_count"],
                "hisa_data_value": result["hisa_data_value"],
                "telco_airtime_count": result["telco_airtime_count"],
                "telco_airtime_value": result["telco_airtime_value"],
                "telco_data_count": result["telco_data_count"],
                "telco_data_value": result["telco_data_value"],
                "hisa_failed_count": result["hisa_failed_count"],
                "hisa_failed_value": result["hisa_failed_value"],
                "telco_failed_count": result["telco_failed_count"],
                "telco_failed_value": result["telco_failed_value"],
                "hisa_success_airtime_count": result["hisa_success_airtime_count"],
                "hisa_success_airtime_value": result["hisa_success_airtime_value"],
                "hisa_success_data_count": result["hisa_success_data_count"],
                "hisa_success_data_value": result["hisa_success_data_value"],
                "telco_success_airtime_count": result["telco_success_airtime_count"],
                "telco_success_airtime_value": result["telco_success_airtime_value"],
                "telco_success_data_count": result["telco_success_data_count"],
                "telco_success_data_value": result["telco_success_data_value"],
            }
        if mno.upper() == "AIRTEL":
            airtel_content = await telco_file.read()
            airtel_dfs = [pd.read_excel(io.BytesIO(airtel_content), dtype=str)]

            if telco_file2:
                airtel_content2 = await telco_file2.read()
                airtel_dfs.append(pd.read_excel(io.BytesIO(airtel_content2), dtype=str))

            airtel_df = pd.concat(airtel_dfs) if len(airtel_dfs) > 1 else airtel_dfs[0]
            cleaned_telco = prepare_airtel_telco(airtel_df)
            result = reconcile_logs(
                mno="AIRTEL",
                target_date=target_date,
                hisa_admin=hisa_df,
                telco_df=cleaned_telco,
                use_transaction_id=use_transaction_id,
            )
            response_data = {
                "hisa_all_count": result["hisa_all_count"],
                "hisa_all_value": result["hisa_all_value"],
                "telco_all_count": result["telco_all_count"],
                "telco_all_value": result["telco_all_value"],
                "hisa_success_count": result["hisa_success_count"],
                "telco_success_count": result["telco_success_count"],
                "matching_statistics": result["matching_statistics"],
                "hisa_total": result["hisa_total"],
                "telco_total": result["telco_total"],
                "difference": result["difference"],
                "missing_in_hisa_count": len(result["missing_in_hisa"]),
                "extra_in_hisa_count": len(result["extra_in_hisa"]),
                "local_duplicates_count": len(result["local_duplicates"]),
                "telco_duplicates_count": len(result["telco_duplicates"]),
                "duplicates_file_url": f"/reports/{mno.upper()}_reconciliation/duplicates_report/{os.path.basename(result['duplicates_file'])}",
                "missing_file_url": f"/reports/{mno.upper()}_reconciliation/missing_report/{os.path.basename(result['missing_file'])}",
                "user_consumption_file_url": f"/reports/{mno.upper()}_reconciliation/user_consumption_report/{os.path.basename(result['user_consumption_file'])}",
                # New fields
                "hisa_airtime_count": result["hisa_airtime_count"],
                "hisa_airtime_value": result["hisa_airtime_value"],
                "hisa_data_count": result["hisa_data_count"],
                "hisa_data_value": result["hisa_data_value"],
                "telco_airtime_count": result["telco_airtime_count"],
                "telco_airtime_value": result["telco_airtime_value"],
                "telco_data_count": result["telco_data_count"],
                "telco_data_value": result["telco_data_value"],
                "hisa_failed_count": result["hisa_failed_count"],
                "hisa_failed_value": result["hisa_failed_value"],
                "telco_failed_count": result["telco_failed_count"],
                "telco_failed_value": result["telco_failed_value"],
                "hisa_success_airtime_count": result["hisa_success_airtime_count"],
                "hisa_success_airtime_value": result["hisa_success_airtime_value"],
                "hisa_success_data_count": result["hisa_success_data_count"],
                "hisa_success_data_value": result["hisa_success_data_value"],
                "telco_success_airtime_count": result["telco_success_airtime_count"],
                "telco_success_airtime_value": result["telco_success_airtime_value"],
                "telco_success_data_count": result["telco_success_data_count"],
                "telco_success_data_value": result["telco_success_data_value"],
            }
        if mno.upper() == "GLO":
            glo_content = await telco_file.read()
            glo_dfs = [pd.read_excel(io.BytesIO(glo_content))]

            if telco_file2:
                glo_content2 = await telco_file2.read()
                glo_dfs.append(pd.read_excel(io.BytesIO(glo_content2)))

            glo_df = pd.concat(glo_dfs) if len(glo_dfs) > 1 else glo_dfs[0]
            cleaned_telco = prepare_glo_telco(glo_df)
            result = reconcile_logs(
                mno="GLO",
                target_date=target_date,
                hisa_admin=hisa_df,
                telco_df=cleaned_telco,
                use_transaction_id=use_transaction_id,
            )
            response_data = {
                "hisa_all_count": result["hisa_all_count"],
                "hisa_all_value": result["hisa_all_value"],
                "telco_all_count": result["telco_all_count"],
                "telco_all_value": result["telco_all_value"],
                "hisa_success_count": result["hisa_success_count"],
                "telco_success_count": result["telco_success_count"],
                "matching_statistics": result["matching_statistics"],
                "hisa_total": result["hisa_total"],
                "telco_total": result["telco_total"],
                "difference": result["difference"],
                "missing_in_hisa_count": len(result["missing_in_hisa"]),
                "extra_in_hisa_count": len(result["extra_in_hisa"]),
                "local_duplicates_count": len(result["local_duplicates"]),
                "telco_duplicates_count": len(result["telco_duplicates"]),
                "duplicates_file_url": f"/reports/{mno.upper()}_reconciliation/duplicates_report/{os.path.basename(result['duplicates_file'])}",
                "missing_file_url": f"/reports/{mno.upper()}_reconciliation/missing_report/{os.path.basename(result['missing_file'])}",
                "user_consumption_file_url": f"/reports/{mno.upper()}_reconciliation/user_consumption_report/{os.path.basename(result['user_consumption_file'])}",
                # New fields
                "hisa_airtime_count": result["hisa_airtime_count"],
                "hisa_airtime_value": result["hisa_airtime_value"],
                "hisa_data_count": result["hisa_data_count"],
                "hisa_data_value": result["hisa_data_value"],
                "telco_airtime_count": result["telco_airtime_count"],
                "telco_airtime_value": result["telco_airtime_value"],
                "telco_data_count": result["telco_data_count"],
                "telco_data_value": result["telco_data_value"],
                "hisa_failed_count": result["hisa_failed_count"],
                "hisa_failed_value": result["hisa_failed_value"],
                "telco_failed_count": result["telco_failed_count"],
                "telco_failed_value": result["telco_failed_value"],
                "hisa_success_airtime_count": result["hisa_success_airtime_count"],
                "hisa_success_airtime_value": result["hisa_success_airtime_value"],
                "hisa_success_data_count": result["hisa_success_data_count"],
                "hisa_success_data_value": result["hisa_success_data_value"],
                "telco_success_airtime_count": result["telco_success_airtime_count"],
                "telco_success_airtime_value": result["telco_success_airtime_value"],
                "telco_success_data_count": result["telco_success_data_count"],
                "telco_success_data_value": result["telco_success_data_value"],
            }
        return JSONResponse(content=response_data)
    except Exception as e:
        return JSONResponse(
            status_code=500, content={"error": f"Error processing files: {str(e)}"}
        )


def _filter_users_with_valid_email(resp: dict) -> dict:
    """Filter users to only include those with valid email addresses."""
    import re

    if not resp.get("status") or not resp.get("data"):
        return resp

    # Email validation regex
    email_pattern = re.compile(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")

    data = resp["data"]
    if isinstance(data, dict) and "data" in data:
        # Handle nested data structure
        users = data["data"]
    elif isinstance(data, list):
        users = data
    else:
        return resp

    # Filter users with valid email addresses
    filtered_users = []
    for user_item in users:
        if isinstance(user_item, dict):
            email = user_item.get("email", "")
            if email and email_pattern.match(str(email)):
                filtered_users.append(user_item)

    # Update the response with filtered users
    if isinstance(data, dict) and "data" in data:
        resp["data"]["data"] = filtered_users
    else:
        resp["data"] = filtered_users

    return resp


@app.get("/hisa-one/users", response_class=JSONResponse)
def get_hisa_one_users(user: dict = Depends(get_current_user)):
    handler = HisaWalletManager()
    resp = handler.fetch_users(hisa_one=True)
    # Filter to only include users with valid email addresses
    filtered_resp = _filter_users_with_valid_email(resp)
    return JSONResponse(
        content=filtered_resp, status_code=filtered_resp.get("status_code", 200)
    )


@app.get("/hisa-two/users", response_class=JSONResponse)
def get_hisa_two_users(user: dict = Depends(get_current_user)):
    handler = HisaWalletManager()
    resp = handler.fetch_users(hisa_two=True)
    # Filter to only include users with valid email addresses
    filtered_resp = _filter_users_with_valid_email(resp)
    return JSONResponse(
        content=filtered_resp, status_code=filtered_resp.get("status_code", 200)
    )


@app.get("/hisa-one/wallets", response_class=JSONResponse)
def get_hisa_one_wallets(user: dict = Depends(get_current_user)):
    handler = HisaWalletManager()
    resp = handler.fetch_wallets(hisa_one=True)
    overall = compute_overall_balance(resp)
    payload = {**resp, "overall_balance": overall}
    return JSONResponse(content=payload, status_code=resp.get("status_code", 200))


@app.get("/hisa-two/wallets", response_class=JSONResponse)
def get_hisa_two_wallets(user: dict = Depends(get_current_user)):
    handler = HisaWalletManager()
    resp = handler.fetch_wallets(hisa_two=True)
    overall = compute_overall_balance(resp)
    payload = {**resp, "overall_balance": overall}
    return JSONResponse(content=payload, status_code=resp.get("status_code", 200))


@app.get("/hisa-one/users/{user_id}/balances/daily", response_class=JSONResponse)
def get_hisa_one_daily_balances(
    user_id: str,
    from_date: str,
    to_date: str,
    user: dict = Depends(get_current_user),
):
    handler = HisaWalletManager()
    resp = handler.daily_balances(
        user_id=user_id, from_date=from_date, to_date=to_date, hisa_one=True
    )
    return JSONResponse(content=resp, status_code=resp.get("status_code", 200))


@app.get("/hisa-two/users/{user_id}/balances/daily", response_class=JSONResponse)
def get_hisa_two_daily_balances(
    user_id: str,
    from_date: str,
    to_date: str,
    user: dict = Depends(get_current_user),
):
    handler = HisaWalletManager()
    resp = handler.daily_balances(
        user_id=user_id, from_date=from_date, to_date=to_date, hisa_two=True
    )
    return JSONResponse(content=resp, status_code=resp.get("status_code", 200))


@app.get("/hisa-one/users/{user_id}/balances/monthly", response_class=JSONResponse)
def get_hisa_one_monthly_balances(
    user_id: str,
    from_date: str,
    to_date: str,
    user: dict = Depends(get_current_user),
):
    handler = HisaWalletManager()
    resp = handler.monthly_balances(
        user_id=user_id, from_date=from_date, to_date=to_date, hisa_one=True
    )
    return JSONResponse(content=resp, status_code=resp.get("status_code", 200))


@app.get("/hisa-two/users/{user_id}/balances/monthly", response_class=JSONResponse)
def get_hisa_two_monthly_balances(
    user_id: str,
    from_date: str,
    to_date: str,
    user: dict = Depends(get_current_user),
):
    handler = HisaWalletManager()
    resp = handler.monthly_balances(
        user_id=user_id, from_date=from_date, to_date=to_date, hisa_two=True
    )
    return JSONResponse(content=resp, status_code=resp.get("status_code", 200))


@app.get("/hisa-one/users/{user_id}/wallet-transactions", response_class=JSONResponse)
def get_hisa_one_wallet_transactions(
    user_id: str,
    from_date: str,
    to_date: str,
    user: dict = Depends(get_current_user),
):
    handler = HisaWalletManager()
    resp = handler.wallet_transactions(
        user_id=user_id, from_date=from_date, to_date=to_date, hisa_one=True
    )
    return JSONResponse(content=resp, status_code=resp.get("status_code", 200))


@app.get("/hisa-two/users/{user_id}/wallet-transactions", response_class=JSONResponse)
def get_hisa_two_wallet_transactions(
    user_id: str,
    from_date: str,
    to_date: str,
    user: dict = Depends(get_current_user),
):
    handler = HisaWalletManager()
    resp = handler.wallet_transactions(
        user_id=user_id, from_date=from_date, to_date=to_date, hisa_two=True
    )
    return JSONResponse(content=resp, status_code=resp.get("status_code", 200))


@app.get("/hisa-one/users/{user_id}/wallet-reconciliation", response_class=JSONResponse)
def reconcile_hisa_one_user(
    user_id: str,
    filter_type: str,
    from_date: Optional[str] = None,
    to_date: Optional[str] = None,
    user: dict = Depends(get_current_user),
):
    start, end = daterange_from_filter(filter_type, from_date, to_date)
    handler = HisaWalletManager()

    wallets_resp = handler.fetch_wallets(hisa_one=True)
    user_wallet = extract_user_from_wallets(wallets_resp, user_id)

    daily_resp = handler.daily_balances(
        user_id=user_id, from_date=start, to_date=end, hisa_one=True
    )
    opening, closing = extract_opening_closing(daily_resp)

    trans_resp = handler.wallet_transactions(
        user_id=user_id, from_date=start, to_date=end, hisa_one=True
    )
    total_debit, total_credit = sum_wallet_transactions(trans_resp)

    expected_closing = opening + total_credit - total_debit
    difference = round(closing - expected_closing, 2)
    balanced = abs(difference) < 0.005

    payload = {
        "status_code": 200,
        "status": True,
        "data": {
            "user_id": str(user_id),
            "period": {"from": start, "to": end},
            "user": user_wallet.get("user"),
            "current_balance": user_wallet.get("current_balance", 0),
            "opening_balance": opening,
            "closing_balance": closing,
            "total_debit": total_debit,
            "total_credit": total_credit,
            "expected_closing_balance": expected_closing,
            "difference": difference,
            "balanced": balanced,
        },
        "error": None,
    }
    return JSONResponse(content=payload, status_code=200)


@app.get("/hisa-two/users/{user_id}/wallet-reconciliation", response_class=JSONResponse)
def reconcile_hisa_two_user(
    user_id: str,
    filter_type: str,
    from_date: Optional[str] = None,
    to_date: Optional[str] = None,
    user: dict = Depends(get_current_user),
):
    start, end = daterange_from_filter(filter_type, from_date, to_date)
    handler = HisaWalletManager()

    wallets_resp = handler.fetch_wallets(hisa_two=True)
    user_wallet = extract_user_from_wallets(wallets_resp, user_id)

    daily_resp = handler.daily_balances(
        user_id=user_id, from_date=start, to_date=end, hisa_two=True
    )
    opening, closing = extract_opening_closing(daily_resp)

    trans_resp = handler.wallet_transactions(
        user_id=user_id, from_date=start, to_date=end, hisa_two=True
    )
    total_debit, total_credit = sum_wallet_transactions(trans_resp)

    expected_closing = opening + total_credit - total_debit
    difference = round(closing - expected_closing, 2)
    balanced = abs(difference) < 0.005

    payload = {
        "status_code": 200,
        "status": True,
        "data": {
            "user_id": str(user_id),
            "period": {"from": start, "to": end},
            "user": user_wallet.get("user"),
            "current_balance": user_wallet.get("current_balance", 0),
            "opening_balance": opening,
            "closing_balance": closing,
            "total_debit": total_debit,
            "total_credit": total_credit,
            "expected_closing_balance": expected_closing,
            "difference": difference,
            "balanced": balanced,
        },
        "error": None,
    }
    return JSONResponse(content=payload, status_code=200)


@app.post("/check-logs/", response_class=JSONResponse)
async def check_user_logs(
    user_id: str = Form(...),
    target_date: str = Form(...),
    hisa_source: str = Form(...),  # "hisa_one" or "hisa_two"
    user: dict = Depends(get_current_user),
):
    """
    Check user logs by downloading transaction data and reconciling with wallet balances.

    Args:
        user_id: The user ID to check logs for
        target_date: Date in YYYY-MM-DD format (cannot be current date)
        hisa_source: Either "hisa_one" or "hisa_two"

    Returns:
        JSON response with log analysis and balance reconciliation
    """
    from datetime import date, datetime
    from app.utils import (
        get_user_transaction_summary,
        get_user_wallet_transactions_summary,
    )

    try:
        # Validate date format and ensure it's not current date
        try:
            target_date_obj = datetime.strptime(target_date, "%Y-%m-%d").date()
        except ValueError:
            return JSONResponse(
                content={
                    "status_code": 400,
                    "status": False,
                    "data": None,
                    "error": "Invalid date format. Please use YYYY-MM-DD format.",
                },
                status_code=400,
            )

        if target_date_obj >= date.today():
            return JSONResponse(
                content={
                    "status_code": 400,
                    "status": False,
                    "data": None,
                    "error": "Cannot select current date or future dates. Please select a past date.",
                },
                status_code=400,
            )

        # Validate hisa_source
        if hisa_source not in ["hisa_one", "hisa_two"]:
            return JSONResponse(
                content={
                    "status_code": 400,
                    "status": False,
                    "data": None,
                    "error": "Invalid hisa_source. Must be 'hisa_one' or 'hisa_two'.",
                },
                status_code=400,
            )

        # Check if logs exist locally first for faster results
        sftp_manager = HisaSFTPManager()
        if hisa_source == "hisa_one":
            local_check = sftp_manager.check_logs_exist_locally(
                target_date, hisa_one=True
            )
        else:
            local_check = sftp_manager.check_logs_exist_locally(
                target_date, hisa_two=True
            )

        if local_check["exists"]:
            download_result = f"Using existing local logs: {local_check['message']}"
        else:
            # Download logs using HisaSFTPManager if not found locally
            try:
                if hisa_source == "hisa_one":
                    sftp_client = sftp_manager.connect(hisa_one=True)
                    download_result = sftp_manager.download_logs(
                        sftp_client, target_date, hisa_one=True
                    )
                else:
                    sftp_client = sftp_manager.connect(hisa_two=True)
                    download_result = sftp_manager.download_logs(
                        sftp_client, target_date, hisa_two=True
                    )

                sftp_client.close()

                if "Failed" in download_result:
                    return JSONResponse(
                        content={
                            "status_code": 500,
                            "status": False,
                            "data": None,
                            "error": f"Failed to download logs: {download_result}",
                        },
                        status_code=500,
                    )

            except Exception as e:
                return JSONResponse(
                    content={
                        "status_code": 500,
                        "status": False,
                        "data": None,
                        "error": f"SFTP connection failed: {str(e)}",
                    },
                    status_code=500,
                )

        # Get daily balances using HisaWalletManager
        wallet_manager = HisaWalletManager()
        try:
            if hisa_source == "hisa_one":
                daily_balances_resp = wallet_manager.daily_balances(
                    user_id, target_date, target_date, hisa_one=True
                )
            else:
                daily_balances_resp = wallet_manager.daily_balances(
                    user_id, target_date, target_date, hisa_two=True
                )

            if not daily_balances_resp.get("status"):
                return JSONResponse(
                    content={
                        "status_code": 500,
                        "status": False,
                        "data": None,
                        "error": "Failed to fetch daily balances from API",
                    },
                    status_code=500,
                )

            # Extract opening and closing balances
            balance_data = daily_balances_resp.get("data", {}).get("data", [])
            if not balance_data:
                opening_balance = 0
                closing_balance = 0
            else:
                balance_record = balance_data[0] if balance_data else {}
                opening_balance = balance_record.get("opening_balance", 0)
                closing_balance = balance_record.get("closing_balance", 0)

        except Exception as e:
            return JSONResponse(
                content={
                    "status_code": 500,
                    "status": False,
                    "data": None,
                    "error": f"Failed to fetch balances: {str(e)}",
                },
                status_code=500,
            )

        # Analyze transaction logs using our utility function
        # Use different file naming patterns for hisa_one vs hisa_two
        if hisa_source == "hisa_one":
            csv_file_path = f"reports/hisa_logs/{hisa_source}/{target_date}/{target_date}_airtime_transactions.csv"
        else:  # hisa_two
            csv_file_path = f"reports/hisa_logs/{hisa_source}/{target_date}/{target_date}_transactions.csv"

        transaction_summary = get_user_transaction_summary(user_id, csv_file_path)

        if "error" in transaction_summary:
            return JSONResponse(
                content={
                    "status_code": 500,
                    "status": False,
                    "data": None,
                    "error": f"Failed to analyze transaction logs: {transaction_summary['error']}",
                },
                status_code=500,
            )

        # Analyze wallet transaction logs
        wallet_csv_file_path = f"reports/hisa_logs/{hisa_source}/{target_date}/{target_date}_wallet_transactions.csv"
        wallet_transaction_summary = get_user_wallet_transactions_summary(
            user_id, wallet_csv_file_path
        )

        if "error" in wallet_transaction_summary:
            return JSONResponse(
                content={
                    "status_code": 500,
                    "status": False,
                    "data": None,
                    "error": f"Failed to analyze wallet transaction logs: {wallet_transaction_summary['error']}",
                },
                status_code=500,
            )

        # Calculate expected closing balance based on successful transactions and wallet credits
        successful_transactions = transaction_summary.get("by_status", {}).get(
            "Successful", {}
        )
        successful_value = successful_transactions.get("value_naira", 0)
        # Get wallet credits and debits (already in kobo from the CSV)
        wallet_credits = wallet_transaction_summary.get("total_credits", 0)
        wallet_debits = wallet_transaction_summary.get("total_debits", 0)
        # Note: successful transactions are already included in wallet debits, so we don't subtract them separately
        expected_closing_balance = opening_balance + wallet_credits - wallet_debits
        balance_difference = closing_balance - expected_closing_balance
        balance_matches = bool(
            abs(balance_difference) < 100
        )  # Allow 1 naira difference due to rounding, ensure it's a Python bool

        # Clean the summaries to ensure JSON serialization
        def clean_for_json(obj):
            """Recursively clean an object to ensure JSON serialization."""
            if isinstance(obj, dict):
                return {k: clean_for_json(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [clean_for_json(item) for item in obj]
            elif hasattr(obj, "item"):  # numpy types
                return obj.item()
            elif isinstance(obj, (int, float, str, bool, type(None))):
                return obj
            else:
                return str(obj)  # Convert anything else to string

        # Clean the summaries
        clean_transaction_summary = clean_for_json(transaction_summary)
        clean_wallet_transaction_summary = clean_for_json(wallet_transaction_summary)

        payload = {
            "status_code": 200,
            "status": True,
            "data": {
                "user_id": str(user_id),
                "target_date": str(target_date),
                "hisa_source": str(hisa_source),
                "download_result": str(download_result),
                "wallet_balances": {
                    "opening_balance": float(opening_balance),
                    "closing_balance": float(closing_balance),
                    "expected_closing_balance": float(expected_closing_balance),
                    "difference": float(balance_difference),
                    "balance_matches": balance_matches,
                },
                "transaction_analysis": clean_transaction_summary,
                "wallet_transaction_analysis": clean_wallet_transaction_summary,
                "reconciliation_summary": {
                    "total_transactions": int(
                        transaction_summary.get("total_transactions", 0)
                    ),
                    "successful_transactions": int(
                        successful_transactions.get("count", 0)
                    ),
                    "successful_value_naira": float(
                        successful_transactions.get("value_naira", 0)
                    ),
                    "total_wallet_credits": float(
                        wallet_transaction_summary.get("total_credits", 0)
                    ),
                    "total_wallet_debits": float(
                        wallet_transaction_summary.get("total_debits", 0)
                    ),
                    "wallet_net_change": float(
                        wallet_transaction_summary.get("net_change", 0)
                    ),
                    "balance_reconciled": balance_matches,
                },
            },
            "error": None,
        }

        return JSONResponse(content=payload, status_code=200)

    except Exception as e:
        return JSONResponse(
            content={
                "status_code": 500,
                "status": False,
                "data": None,
                "error": f"Unexpected error: {str(e)}",
            },
            status_code=500,
        )


@app.post("/telco-summary", response_class=JSONResponse)
async def get_enhanced_telco_summary(
    target_date: str = Form(...),
    hisa_source: str = Form("hisa_one"),
    # MTN file uploads (optional)
    mtn_file1: UploadFile = File(None),
    mtn_file2: UploadFile = File(None),
    mtn_file3: UploadFile = File(None),
    # Airtel file uploads (optional)
    airtel_file1: UploadFile = File(None),
    airtel_file2: UploadFile = File(None),
    airtel_file3: UploadFile = File(None),
    # GLO file uploads (optional)
    glo_file1: UploadFile = File(None),
    glo_file2: UploadFile = File(None),
    glo_file3: UploadFile = File(None),
    user: dict = Depends(get_current_user),
):
    """
    Get enhanced transaction summary by telco and status for a specific date.
    Optionally accepts telco files for enhanced reconciliation analysis.

    Args:
        target_date (str): Date in YYYY-MM-DD format
        hisa_source (str): Either 'hisa_one' or 'hisa_two'
        mtn_file1, mtn_file2, mtn_file3: Optional MTN files
        airtel_file1, airtel_file2, airtel_file3: Optional Airtel files
        glo_file1, glo_file2, glo_file3: Optional GLO files

    Returns:
        JSONResponse: Enhanced summary of transactions by telco and status
    """
    try:
        # Validate hisa_source
        if hisa_source not in ["hisa_one", "hisa_two"]:
            return JSONResponse(
                content={
                    "status_code": 400,
                    "status": False,
                    "data": None,
                    "error": "Invalid hisa_source. Must be 'hisa_one' or 'hisa_two'.",
                },
                status_code=400,
            )

        # Process uploaded telco files
        telco_data = {}

        # Process MTN files
        mtn_files = [f for f in [mtn_file1, mtn_file2, mtn_file3] if f is not None]
        if mtn_files:
            mtn_dfs = []
            for mtn_file in mtn_files:
                mtn_content = await mtn_file.read()
                mtn_df = pd.read_excel(io.BytesIO(mtn_content))
                mtn_dfs.append(mtn_df)

            combined_mtn_df = pd.concat(mtn_dfs) if len(mtn_dfs) > 1 else mtn_dfs[0]
            cleaned_mtn = prepare_mtn_telco(combined_mtn_df)
            if not isinstance(cleaned_mtn, str):  # Check if processing was successful
                telco_data["MTN"] = cleaned_mtn

        # Process Airtel files
        airtel_files = [
            f for f in [airtel_file1, airtel_file2, airtel_file3] if f is not None
        ]
        if airtel_files:
            airtel_dfs = []
            for airtel_file in airtel_files:
                airtel_content = await airtel_file.read()
                airtel_df = pd.read_excel(io.BytesIO(airtel_content), dtype=str)
                airtel_dfs.append(airtel_df)

            combined_airtel_df = (
                pd.concat(airtel_dfs) if len(airtel_dfs) > 1 else airtel_dfs[0]
            )
            cleaned_airtel = prepare_airtel_telco(combined_airtel_df)
            if not isinstance(
                cleaned_airtel, str
            ):  # Check if processing was successful
                telco_data["AIRTEL"] = cleaned_airtel

        # Process GLO files
        glo_files = [f for f in [glo_file1, glo_file2, glo_file3] if f is not None]
        if glo_files:
            glo_dfs = []
            for glo_file in glo_files:
                glo_content = await glo_file.read()
                glo_df = pd.read_excel(io.BytesIO(glo_content))
                glo_dfs.append(glo_df)

            combined_glo_df = pd.concat(glo_dfs) if len(glo_dfs) > 1 else glo_dfs[0]
            cleaned_glo = prepare_glo_telco(combined_glo_df)
            if not isinstance(cleaned_glo, str):  # Check if processing was successful
                telco_data["GLO"] = cleaned_glo

        # Get enhanced telco transaction summary
        from app.utils import get_enhanced_telco_transaction_summary

        summary = get_enhanced_telco_transaction_summary(
            target_date, hisa_source, telco_data
        )

        if "error" in summary:
            return JSONResponse(
                content={
                    "status_code": 404,
                    "status": False,
                    "data": None,
                    "error": summary["error"],
                },
                status_code=404,
            )

        return JSONResponse(
            content={
                "status_code": 200,
                "status": True,
                "data": summary,
            },
            status_code=200,
        )

    except Exception as e:
        return JSONResponse(
            content={
                "status_code": 500,
                "status": False,
                "data": None,
                "error": f"Unexpected error: {str(e)}",
            },
            status_code=500,
        )


# Keep the old GET endpoint for backward compatibility
@app.get("/telco-summary/{target_date}", response_class=JSONResponse)
def get_telco_summary(
    target_date: str,
    hisa_source: str = "hisa_one",
    user: dict = Depends(get_current_user),
):
    """
    Get transaction summary by telco and status for a specific date.
    (Legacy endpoint for backward compatibility)

    Args:
        target_date (str): Date in YYYY-MM-DD format
        hisa_source (str): Either 'hisa_one' or 'hisa_two'

    Returns:
        JSONResponse: Summary of transactions by telco and status
    """
    try:
        # Validate hisa_source
        if hisa_source not in ["hisa_one", "hisa_two"]:
            return JSONResponse(
                content={
                    "status_code": 400,
                    "status": False,
                    "data": None,
                    "error": "Invalid hisa_source. Must be 'hisa_one' or 'hisa_two'.",
                },
                status_code=400,
            )

        # Get telco transaction summary (without uploaded files)
        summary = get_telco_transaction_summary(target_date, hisa_source)

        if "error" in summary:
            return JSONResponse(
                content={
                    "status_code": 404,
                    "status": False,
                    "data": None,
                    "error": summary["error"],
                },
                status_code=404,
            )

        return JSONResponse(
            content={
                "status_code": 200,
                "status": True,
                "data": summary,
            },
            status_code=200,
        )

    except Exception as e:
        return JSONResponse(
            content={
                "status_code": 500,
                "status": False,
                "data": None,
                "error": f"Unexpected error: {str(e)}",
            },
            status_code=500,
        )

    except Exception as e:
        return JSONResponse(
            content={
                "status_code": 500,
                "status": False,
                "data": None,
                "error": f"Unexpected error: {str(e)}",
            },
            status_code=500,
        )


@app.get("/telco-user-consumption/{target_date}/{telco}", response_class=JSONResponse)
def get_telco_user_consumption(
    target_date: str,
    telco: str,
    hisa_source: str = "hisa_one",
    user: dict = Depends(get_current_user),
):
    """
    Get detailed user consumption summary for a specific telco.

    Args:
        target_date (str): Date in YYYY-MM-DD format
        telco (str): Telco name (MTN, AIRTEL, GLO)
        hisa_source (str): Either 'hisa_one' or 'hisa_two'

    Returns:
        JSONResponse: Detailed user consumption for the specified telco
    """
    try:
        # Validate hisa_source
        if hisa_source not in ["hisa_one", "hisa_two"]:
            return JSONResponse(
                content={
                    "status_code": 400,
                    "status": False,
                    "data": None,
                    "error": "Invalid hisa_source. Must be 'hisa_one' or 'hisa_two'.",
                },
                status_code=400,
            )

        # Validate telco
        valid_telcos = ["MTN", "AIRTEL", "GLO"]
        if telco.upper() not in valid_telcos:
            return JSONResponse(
                content={
                    "status_code": 400,
                    "status": False,
                    "data": None,
                    "error": f"Invalid telco. Must be one of: {', '.join(valid_telcos)}",
                },
                status_code=400,
            )

        # Get user consumption summary
        consumption = get_telco_user_consumption_summary(
            target_date, telco, hisa_source
        )

        if "error" in consumption:
            return JSONResponse(
                content={
                    "status_code": 404,
                    "status": False,
                    "data": None,
                    "error": consumption["error"],
                },
                status_code=404,
            )

        return JSONResponse(
            content={
                "status_code": 200,
                "status": True,
                "data": consumption,
                "error": None,
            },
            status_code=200,
        )

    except Exception as e:
        return JSONResponse(
            content={
                "status_code": 500,
                "status": False,
                "data": None,
                "error": f"Unexpected error: {str(e)}",
            },
            status_code=500,
        )


@app.get("/user-details", response_class=JSONResponse)
def get_user_details(user: dict = Depends(get_current_user)):
    """
    Get user details from users.csv file.

    Returns:
        JSON response with user details mapping
    """
    try:
        import pandas as pd
        import os

        # Try to find users.csv in common locations
        possible_paths = [
            "users.csv",
            "data/users.csv",
            "files/users.csv",
            "../users.csv",
        ]

        users_file = None
        for path in possible_paths:
            if os.path.exists(path):
                users_file = path
                break

        if not users_file:
            print("Warning: users.csv file not found in any expected location")
            return JSONResponse(content={"status": True, "data": {}})

        # Read users CSV
        df = pd.read_csv(users_file)

        # Create user details mapping
        user_details = {}
        for _, row in df.iterrows():
            user_id = str(row.get("id", ""))
            name = str(row.get("name", ""))
            email = str(row.get("email", ""))

            if user_id and name and email:
                user_details[user_id] = {"id": user_id, "name": name, "email": email}

        return JSONResponse(content={"status": True, "data": user_details})

    except Exception as e:
        print(f"Error getting user details: {str(e)}")
        return JSONResponse(
            content={"status": False, "error": f"Failed to get user details: {str(e)}"},
            status_code=500,
        )
