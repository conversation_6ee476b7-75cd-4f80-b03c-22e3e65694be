"""
Dependency Injection Container.

Implements the Dependency Inversion Principle by providing a centralized
container for managing dependencies and their lifecycles.
"""

from typing import Dict, Any, TypeVar, Type, Callable, Optional
from functools import lru_cache
import inspect

from app.core.config import AppConfig, get_config
from app.core.exceptions import ApplicationError

T = TypeVar('T')


class Container:
    """
    Dependency injection container that manages service instances
    and their dependencies following SOLID principles.
    """
    
    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._factories: Dict[str, Callable] = {}
        self._singletons: Dict[str, Any] = {}
        self._config = get_config()
        
        # Register core services
        self._register_core_services()
    
    def register_singleton(self, interface: Type[T], implementation: Type[T]) -> None:
        """Register a singleton service."""
        key = self._get_key(interface)
        self._factories[key] = lambda: self._create_instance(implementation)
        
    def register_transient(self, interface: Type[T], implementation: Type[T]) -> None:
        """Register a transient service (new instance each time)."""
        key = self._get_key(interface)
        self._services[key] = lambda: self._create_instance(implementation)
    
    def register_instance(self, interface: Type[T], instance: T) -> None:
        """Register a specific instance."""
        key = self._get_key(interface)
        self._singletons[key] = instance
    
    def get(self, interface: Type[T]) -> T:
        """Get service instance."""
        key = self._get_key(interface)
        
        # Check singletons first
        if key in self._singletons:
            return self._singletons[key]
        
        # Check singleton factories
        if key in self._factories:
            if key not in self._singletons:
                self._singletons[key] = self._factories[key]()
            return self._singletons[key]
        
        # Check transient services
        if key in self._services:
            return self._services[key]()
        
        # Try to auto-wire
        try:
            return self._create_instance(interface)
        except Exception as e:
            raise ApplicationError(f"Cannot resolve dependency: {interface.__name__}") from e
    
    def _get_key(self, interface: Type) -> str:
        """Get string key for interface."""
        return f"{interface.__module__}.{interface.__name__}"
    
    def _create_instance(self, cls: Type[T]) -> T:
        """Create instance with dependency injection."""
        signature = inspect.signature(cls.__init__)
        kwargs = {}
        
        for param_name, param in signature.parameters.items():
            if param_name == 'self':
                continue
                
            if param.annotation != inspect.Parameter.empty:
                try:
                    kwargs[param_name] = self.get(param.annotation)
                except ApplicationError:
                    if param.default != inspect.Parameter.empty:
                        kwargs[param_name] = param.default
                    else:
                        raise
        
        return cls(**kwargs)
    
    def _register_core_services(self) -> None:
        """Register core application services."""
        # Register configuration
        self.register_instance(AppConfig, self._config)
        
        # Database session will be registered by database module
        # Services will be registered by their respective modules


# Global container instance
_container: Optional[Container] = None


def get_container() -> Container:
    """Get global container instance."""
    global _container
    if _container is None:
        _container = Container()
    return _container


def inject(interface: Type[T]) -> T:
    """Dependency injection decorator/function."""
    return get_container().get(interface)
