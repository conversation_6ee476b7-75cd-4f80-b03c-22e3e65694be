"""
Custom exceptions for the HISA Reconciliation Manager.

Follows the principle of explicit error handling and provides
meaningful error messages for different failure scenarios.
"""

from typing import Optional, Dict, Any


class ApplicationError(Exception):
    """Base exception for all application errors."""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}


class ValidationError(ApplicationError):
    """Raised when data validation fails."""
    
    def __init__(
        self, 
        message: str, 
        field: Optional[str] = None,
        value: Optional[Any] = None,
        **kwargs
    ):
        super().__init__(message, error_code="VALIDATION_ERROR", **kwargs)
        self.field = field
        self.value = value


class ProcessingError(ApplicationError):
    """Raised when data processing fails."""
    
    def __init__(self, message: str, stage: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="PROCESSING_ERROR", **kwargs)
        self.stage = stage


class ReconciliationError(ApplicationError):
    """Raised when reconciliation operations fail."""
    
    def __init__(
        self, 
        message: str, 
        telco: Optional[str] = None,
        transaction_count: Optional[int] = None,
        **kwargs
    ):
        super().__init__(message, error_code="RECONCILIATION_ERROR", **kwargs)
        self.telco = telco
        self.transaction_count = transaction_count


class FileProcessingError(ApplicationError):
    """Raised when file processing fails."""
    
    def __init__(
        self, 
        message: str, 
        filename: Optional[str] = None,
        file_type: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, error_code="FILE_PROCESSING_ERROR", **kwargs)
        self.filename = filename
        self.file_type = file_type


class AuthenticationError(ApplicationError):
    """Raised when authentication fails."""
    
    def __init__(self, message: str = "Authentication failed", **kwargs):
        super().__init__(message, error_code="AUTHENTICATION_ERROR", **kwargs)


class AuthorizationError(ApplicationError):
    """Raised when authorization fails."""
    
    def __init__(self, message: str = "Access denied", **kwargs):
        super().__init__(message, error_code="AUTHORIZATION_ERROR", **kwargs)
