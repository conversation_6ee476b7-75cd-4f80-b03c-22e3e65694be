"""
Application configuration management.

Centralized configuration following the Single Responsibility Principle.
Provides type-safe configuration access and environment-based settings.
"""

from typing import Optional, List
from pydantic import BaseSettings, Field
from functools import lru_cache
import os


class DatabaseConfig(BaseSettings):
    """Database configuration settings."""
    
    url: str = Field(..., env="DATABASE_URL")
    pool_size: int = Field(10, env="DB_POOL_SIZE")
    max_overflow: int = Field(20, env="DB_MAX_OVERFLOW")
    pool_timeout: int = Field(30, env="DB_POOL_TIMEOUT")
    timezone: str = Field("UTC", env="TIMEZONE")
    
    class Config:
        env_prefix = "DB_"


class APIConfig(BaseSettings):
    """API configuration settings."""
    
    title: str = Field("HISA Reconciliation Manager", env="API_TITLE")
    version: str = Field("2.0.0", env="API_VERSION")
    description: str = Field("Advanced reconciliation system", env="API_DESCRIPTION")
    max_request_size: int = Field(100 * 1024 * 1024, env="API_MAX_REQUEST_SIZE")  # 100MB
    
    # CORS settings
    cors_origins: List[str] = Field(["*"], env="CORS_ORIGINS")
    cors_credentials: bool = Field(True, env="CORS_CREDENTIALS")
    cors_methods: List[str] = Field(["*"], env="CORS_METHODS")
    cors_headers: List[str] = Field(["*"], env="CORS_HEADERS")
    
    class Config:
        env_prefix = "API_"


class SecurityConfig(BaseSettings):
    """Security configuration settings."""
    
    secret_key: str = Field(..., env="SECRET_KEY")
    algorithm: str = Field("HS256", env="JWT_ALGORITHM")
    access_token_expire_minutes: int = Field(30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    refresh_token_expire_days: int = Field(7, env="REFRESH_TOKEN_EXPIRE_DAYS")
    
    class Config:
        env_prefix = "SECURITY_"


class HisaConfig(BaseSettings):
    """HISA service configuration."""
    
    hisa_one_base_url: str = Field(..., env="HISA_ONE_BASE_URL")
    hisa_two_base_url: str = Field(..., env="HISA_TWO_BASE_URL")
    api_auth_token: str = Field(..., env="HISA_API_AUTH_TOKEN")
    timeout: int = Field(30, env="HISA_TIMEOUT")
    
    class Config:
        env_prefix = "HISA_"


class FileConfig(BaseSettings):
    """File processing configuration."""
    
    base_dir: str = Field(..., env="BASE_DIR")
    upload_dir: str = Field("uploads", env="UPLOAD_DIR")
    reports_dir: str = Field("reports", env="REPORTS_DIR")
    max_file_size: int = Field(50 * 1024 * 1024, env="MAX_FILE_SIZE")  # 50MB
    allowed_extensions: List[str] = Field([".xlsx", ".xls", ".csv"], env="ALLOWED_EXTENSIONS")
    
    class Config:
        env_prefix = "FILE_"


class AppConfig(BaseSettings):
    """Main application configuration."""
    
    environment: str = Field("development", env="ENVIRONMENT")
    debug: bool = Field(False, env="DEBUG")
    log_level: str = Field("INFO", env="LOG_LEVEL")
    
    # Sub-configurations
    database: DatabaseConfig = DatabaseConfig()
    api: APIConfig = APIConfig()
    security: SecurityConfig = SecurityConfig()
    hisa: HisaConfig = HisaConfig()
    file: FileConfig = FileConfig()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


@lru_cache()
def get_config() -> AppConfig:
    """Get cached application configuration."""
    return AppConfig()
