"""
Enhanced System Tests

Comprehensive test suite for the overhauled HISA Reconciliation Manager
testing SOLID principles, performance, and user experience.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from sqlmodel import Session, create_engine
from sqlmodel.pool import StaticPool

from app.main_v2 import app
from app.core.container import get_container
from app.core.config import get_config
from app.core.exceptions import ValidationError, AuthenticationError, ReconciliationError
from app.services.enhanced_auth_service import EnhancedAuthService
from app.services.enhanced_reconciliation_service import EnhancedReconciliationService


class TestEnhancedAuthService:
    """Test suite for enhanced authentication service."""
    
    @pytest.fixture
    def auth_service(self):
        """Create auth service instance for testing."""
        config = get_config()
        user_repo = Mock()
        return EnhancedAuthService(user_repo, config)
    
    @pytest.fixture
    def mock_user(self):
        """Mock user data for testing."""
        user = Mock()
        user.uid = "test-user-id"
        user.email = "<EMAIL>"
        user.hashed_password = "$2b$12$test.hash.value"
        user.is_active = True
        user.is_admin = False
        return user
    
    @pytest.mark.asyncio
    async def test_authenticate_user_success(self, auth_service, mock_user):
        """Test successful user authentication."""
        # Mock repository
        auth_service._user_repo.get_by_email = AsyncMock(return_value=mock_user)
        auth_service._user_repo.update_last_login = AsyncMock()
        
        # Mock password verification
        with patch.object(auth_service, '_verify_password', return_value=True):
            result = await auth_service.authenticate_user("<EMAIL>", "password")
        
        assert result is not None
        assert result["email"] == "<EMAIL>"
        assert result["user_id"] == "test-user-id"
        auth_service._user_repo.update_last_login.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_authenticate_user_invalid_credentials(self, auth_service):
        """Test authentication with invalid credentials."""
        auth_service._user_repo.get_by_email = AsyncMock(return_value=None)
        
        with pytest.raises(AuthenticationError):
            await auth_service.authenticate_user("<EMAIL>", "password")
    
    @pytest.mark.asyncio
    async def test_generate_tokens(self, auth_service):
        """Test JWT token generation."""
        user_data = {
            "user_id": "test-user-id",
            "email": "<EMAIL>",
            "is_admin": False
        }
        
        tokens = await auth_service.generate_tokens(user_data)
        
        assert "access_token" in tokens
        assert "refresh_token" in tokens
        assert tokens["token_type"] == "bearer"
        assert isinstance(tokens["expires_in"], int)
    
    @pytest.mark.asyncio
    async def test_validate_token(self, auth_service):
        """Test token validation."""
        user_data = {
            "user_id": "test-user-id",
            "email": "<EMAIL>",
            "is_admin": False
        }
        
        tokens = await auth_service.generate_tokens(user_data)
        result = await auth_service.validate_token(tokens["access_token"])
        
        assert result is not None
        assert result["user_id"] == "test-user-id"
        assert result["email"] == "<EMAIL>"
    
    def test_password_strength_validation(self, auth_service):
        """Test password strength validation."""
        # Valid password
        auth_service._validate_password_strength("StrongPass123!")
        
        # Invalid passwords
        with pytest.raises(ValidationError):
            auth_service._validate_password_strength("weak")  # Too short
        
        with pytest.raises(ValidationError):
            auth_service._validate_password_strength("nouppercase123!")  # No uppercase
        
        with pytest.raises(ValidationError):
            auth_service._validate_password_strength("NOLOWERCASE123!")  # No lowercase


class TestEnhancedReconciliationService:
    """Test suite for enhanced reconciliation service."""
    
    @pytest.fixture
    def reconciliation_service(self):
        """Create reconciliation service instance for testing."""
        config = get_config()
        transaction_repo = Mock()
        return EnhancedReconciliationService(transaction_repo, config)
    
    @pytest.fixture
    def sample_hisa_data(self):
        """Sample HISA data for testing."""
        import pandas as pd
        return pd.DataFrame({
            'transaction_id': ['TXN001', 'TXN002', 'TXN003'],
            'amount': [10000, 20000, 15000],
            'msisdn': ['2348012345678', '2348087654321', '2348098765432'],
            'Date': ['2024-01-15', '2024-01-15', '2024-01-15']
        })
    
    @pytest.fixture
    def sample_telco_data(self):
        """Sample telco data for testing."""
        import pandas as pd
        return pd.DataFrame({
            'TxnId': ['TXN001', 'TXN002', 'TXN004'],
            'Amount': [10000, 20000, 25000],
            'MSISDN': ['2348012345678', '2348087654321', '2348011111111'],
            'Date': ['2024-01-15', '2024-01-15', '2024-01-15']
        })
    
    @pytest.mark.asyncio
    async def test_reconcile_transactions_success(
        self, 
        reconciliation_service, 
        sample_hisa_data, 
        sample_telco_data
    ):
        """Test successful transaction reconciliation."""
        result = await reconciliation_service.reconcile_transactions(
            hisa_data=sample_hisa_data,
            telco_data=sample_telco_data,
            telco="mtn",
            target_date="2024-01-15"
        )
        
        assert "reconciliation_id" in result
        assert result["telco"] == "mtn"
        assert result["target_date"] == "2024-01-15"
        assert "statistics" in result
        assert "insights" in result
    
    @pytest.mark.asyncio
    async def test_reconcile_transactions_empty_data(self, reconciliation_service):
        """Test reconciliation with empty data."""
        import pandas as pd
        
        empty_df = pd.DataFrame()
        sample_df = pd.DataFrame({
            'TxnId': ['TXN001'],
            'Amount': [10000],
            'MSISDN': ['2348012345678'],
            'Date': ['2024-01-15']
        })
        
        with pytest.raises(ValidationError):
            await reconciliation_service.reconcile_transactions(
                hisa_data=empty_df,
                telco_data=sample_df,
                telco="mtn",
                target_date="2024-01-15"
            )


class TestAPIEndpoints:
    """Test suite for API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def auth_headers(self):
        """Mock authentication headers."""
        return {"Authorization": "Bearer test-token"}
    
    def test_health_endpoint(self, client):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        assert "status" in response.json()
        assert response.json()["status"] == "healthy"
    
    def test_root_endpoint(self, client):
        """Test root endpoint."""
        response = client.get("/")
        assert response.status_code == 200
        assert "name" in response.json()
        assert "version" in response.json()
    
    @patch('app.api.auth_router.inject')
    def test_login_endpoint(self, mock_inject, client):
        """Test login endpoint."""
        mock_auth_service = Mock()
        mock_auth_service.authenticate_user = AsyncMock(return_value={
            "user_id": "test-id",
            "email": "<EMAIL>",
            "is_admin": False
        })
        mock_auth_service.generate_tokens = AsyncMock(return_value={
            "access_token": "test-token",
            "refresh_token": "test-refresh",
            "token_type": "bearer",
            "expires_in": 1800
        })
        mock_inject.return_value = mock_auth_service
        
        response = client.post("/api/v2/auth/login", data={
            "username": "<EMAIL>",
            "password": "password"
        })
        
        assert response.status_code == 200
        assert "access_token" in response.json()


class TestPerformance:
    """Test suite for performance optimizations."""
    
    def test_container_singleton_behavior(self):
        """Test that container maintains singleton behavior."""
        container = get_container()
        container2 = get_container()
        
        assert container is container2
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self):
        """Test system behavior under concurrent load."""
        client = TestClient(app)
        
        async def make_request():
            response = client.get("/health")
            return response.status_code
        
        # Simulate concurrent requests
        tasks = [make_request() for _ in range(10)]
        results = await asyncio.gather(*tasks)
        
        # All requests should succeed
        assert all(status == 200 for status in results)
    
    def test_memory_usage_optimization(self):
        """Test memory usage patterns."""
        import gc
        import sys
        
        # Get initial memory usage
        gc.collect()
        initial_objects = len(gc.get_objects())
        
        # Create and destroy multiple service instances
        for _ in range(100):
            container = get_container()
            # Simulate service usage
            
        gc.collect()
        final_objects = len(gc.get_objects())
        
        # Memory usage should not grow significantly
        growth_ratio = final_objects / initial_objects
        assert growth_ratio < 1.5  # Allow for some growth but not excessive


class TestUserExperience:
    """Test suite for user experience features."""
    
    def test_error_handling_consistency(self, client):
        """Test consistent error response format."""
        # Test various error scenarios
        error_endpoints = [
            ("/api/v2/auth/validate", {"token": "invalid"}),
            ("/api/v2/reconciliation/process", {}),
        ]
        
        for endpoint, data in error_endpoints:
            response = client.post(endpoint, json=data)
            
            # All errors should have consistent structure
            if response.status_code >= 400:
                error_data = response.json()
                assert "error" in error_data
                assert "message" in error_data
    
    def test_response_time_requirements(self, client):
        """Test API response time requirements."""
        import time
        
        start_time = time.time()
        response = client.get("/health")
        end_time = time.time()
        
        response_time = end_time - start_time
        
        # Health check should respond within 100ms
        assert response_time < 0.1
        assert response.status_code == 200


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
