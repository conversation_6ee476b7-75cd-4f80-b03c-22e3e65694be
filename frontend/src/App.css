/* Professional Base Layout */
html {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: 100vh;
  position: relative;
  background: transparent;
}

/* Professional Container Styling */
.container {
  position: relative;
  padding: 2rem 1rem;
  max-width: 1400px;
}

/* Enhanced Form Container */
.form-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  width: 100%;
  padding: 2rem 0;
  position: relative;
  background: transparent;
}

/* Enhanced Telco Summary Styles */
.telco-upload-section {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.telco-upload-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.telco-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.telco-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.stats-mini-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stats-mini-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(66, 165, 245, 0.2);
}

.hover-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

.page-transition {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.icon-circle {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.icon-circle:hover {
  transform: scale(1.1);
}

/* Enhanced File Upload Styles */
.uploaded-files {
  max-height: 200px;
  overflow-y: auto;
}

.uploaded-files::-webkit-scrollbar {
  width: 4px;
}

.uploaded-files::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.uploaded-files::-webkit-scrollbar-thumb {
  background: rgba(66, 165, 245, 0.5);
  border-radius: 2px;
}

.uploaded-files::-webkit-scrollbar-thumb:hover {
  background: rgba(66, 165, 245, 0.7);
}

/* Enhanced Button Styles */
.btn-primary {
  background: linear-gradient(135deg, #4299e1 0%, #63b3ed 100%);
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #3182ce 0%, #4299e1 100%);
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(66, 153, 225, 0.4);
}

.btn-outline-primary {
  border: 2px solid #4299e1;
  color: #4299e1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-outline-primary:hover {
  background: #4299e1;
  border-color: #4299e1;
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(66, 153, 225, 0.3);
}

/* Professional Card Styling */
.card {
  background-color: rgba(255, 255, 255, 0.98);
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  border: none;
  border-radius: 24px;
  backdrop-filter: blur(20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: 0 35px 70px rgba(0, 0, 0, 0.2);
}

.dark-theme .card {
  background-color: rgba(42, 42, 42, 0.95);
  backdrop-filter: blur(20px);
}

/* Professional Icon Styling */
.icon-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.icon-circle:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.icon-circle i {
  font-size: 2rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Professional Logo Styling */
.logo {
  height: 4em;
  padding: 1em;
  will-change: filter;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 16px;
}

.logo:hover {
  filter: drop-shadow(0 8px 32px rgba(102, 126, 234, 0.4));
  transform: translateY(-4px) scale(1.05);
}

/* Professional Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

.page-transition {
  animation: fadeInUp 0.6s ease-out;
}

.slide-in {
  animation: slideInRight 0.5s ease-out;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* Professional Dark Theme */
.dark-theme {
  color: #ffffff;
  min-height: 100vh;
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 50%, #2b6cb0 100%);
  background-attachment: fixed;
}

.dark-theme html,
.dark-theme body,
.dark-theme #root,
.dark-theme .form-container {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 50%, #2b6cb0 100%);
  background-attachment: fixed;
}

/* Enhanced Reconciliation Form */
.reconciliation-form {
  background-color: rgba(255, 255, 255, 0.98);
  padding: 2rem;
  border-radius: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.reconciliation-form:hover {
  transform: translateY(-2px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

@media (min-width: 768px) {
  .reconciliation-form {
    padding: 3rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 3rem 2rem;
  }
}

.dark-theme .form-control,
.dark-theme .form-select {
  background-color: #2d2d2d;
  border-color: #3d3d3d;
  color: #ffffff;
}

.dark-theme .form-control:focus,
.dark-theme .form-select:focus {
  background-color: #2d2d2d;
  border-color: #4d4d4d;
  color: #ffffff;
  box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25);
}

.dark-theme .form-label {
  color: #ffffff;
}

.dark-theme .modal-content {
  background-color: #2d2d2d;
  color: #ffffff;
}

.dark-theme .modal-header {
  border-bottom-color: #3d3d3d;
}

.dark-theme .modal-footer {
  border-top-color: #3d3d3d;
}

.dark-theme .btn-outline-light:hover {
  background-color: #ffffff;
  color: #1a1a1a;
}

.dark-theme a {
  color: #4dabf7;
}

.dark-theme a:hover {
  color: #74c0fc;
}

.dark-theme .alert-danger {
  background-color: #842029;
  border-color: #842029;
  color: #ffffff;
}

.dark-theme .table {
  color: #ffffff;
}

.dark-theme .table td,
.dark-theme .table th {
  border-color: #3d3d3d;
}

.dark-theme .table-striped>tbody>tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}

.dark-theme .table-hover>tbody>tr:hover {
  background-color: rgba(255, 255, 255, 0.075);
}

.stat-card {
  background-color: #ffffff;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
}

.stat-card h4 {
  color: #2c3e50;
  margin-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 10px;
}

.results-page {
  padding: 2rem 0;
}

.download-links {
  background-color: #ffffff;
  padding: 1.5rem;
  border-radius: 8px;
  margin-top: 2rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
}

.download-links h4 {
  color: #2c3e50;
  margin-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 10px;
}

.table {
  margin-bottom: 0;
}

.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-warning {
  color: #ffc107 !important;
}

.table-dark {
  background-color: #2d2d2d;
}

.table-dark td,
.table-dark th {
  border-color: #444;
}

.btn-outline-light:hover {
  background-color: #444;
  color: #fff;
}

/* Professional Form Sections */
.form-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.form-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px 20px 0 0;
}

.form-section:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.section-title {
  color: #2d3748;
  font-weight: 700;
  font-size: 1.4rem;
  position: relative;
  padding-bottom: 1rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-title::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.section-title i {
  color: #667eea;
  font-size: 1.2em;
}

/* Professional Form Controls */
.form-control-custom,
.form-select-custom {
  height: 3.5rem;
  border: 2px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  padding: 0.75rem 1.25rem;
  font-size: 16px;
  font-weight: 500;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

@media (min-width: 768px) {

  .form-control-custom,
  .form-select-custom {
    height: 4rem;
    padding: 1rem 1.5rem;
    font-size: 17px;
  }
}

.form-control-custom:focus,
.form-select-custom:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15);
  transform: translateY(-2px);
  background-color: rgba(255, 255, 255, 1);
}

.form-control-custom:hover:not(:disabled),
.form-select-custom:hover:not(:disabled) {
  border-color: #764ba2;
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

/* Professional File Upload Cards */
.file-upload-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.9));
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 1.5rem;
  border: 2px solid rgba(226, 232, 240, 0.5);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.file-upload-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px 20px 0 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.file-upload-card:hover::before {
  opacity: 1;
}

@media (min-width: 768px) {
  .file-upload-card {
    padding: 2.5rem;
    margin-bottom: 2rem;
  }
}

.file-upload-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
  border-color: rgba(102, 126, 234, 0.3);
}

.file-upload-header {
  display: flex;
  align-items: center;
  gap: 1.25rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1.25rem;
  border-bottom: 2px solid rgba(226, 232, 240, 0.6);
}

.file-icon {
  font-size: 2rem;
  color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.upload-box {
  border: 3px dashed rgba(102, 126, 234, 0.3);
  border-radius: 16px;
  padding: 2rem 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.8));
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.upload-box::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
  transition: all 0.4s ease;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

@media (min-width: 768px) {
  .upload-box {
    padding: 3rem 2rem;
  }
}

.upload-box:hover {
  border-color: #667eea;
  background: linear-gradient(145deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(102, 126, 234, 0.15);
}

.upload-box:hover::before {
  width: 200px;
  height: 200px;
}

.upload-icon {
  font-size: 3rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: block;
  margin-bottom: 1.25rem;
  transition: all 0.3s ease;
}

.upload-box:hover .upload-icon {
  transform: scale(1.1) rotate(5deg);
}

.upload-text {
  display: block;
  color: #4a5568;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  transition: color 0.3s ease;
}

.upload-box:hover .upload-text {
  color: #2d3748;
}

.selected-file {
  display: block;
  color: #667eea;
  font-size: 1rem;
  font-weight: 700;
  margin-top: 1rem;
  word-break: break-all;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  padding: 0.75rem 1rem;
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  backdrop-filter: blur(5px);
  animation: slideInRight 0.3s ease;
}

/* Options Card */
.options-card {
  background: #f8f9fa;
  border-radius: 1rem;
  padding: 1.75rem;
  border: 1px solid #e9ecef;
  margin-bottom: 2rem;
}

.custom-checkbox {
  font-size: 1.1rem;
  font-weight: 500;
}

/* Professional Submit Button */
.submit-button {
  height: 4rem;
  border-radius: 20px;
  font-weight: 700;
  font-size: 1.2rem;
  padding: 1rem 2rem;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submit-button:hover::before {
  left: 100%;
}

@media (min-width: 768px) {
  .submit-button {
    height: 4.5rem;
    font-size: 1.3rem;
    padding: 1.25rem 2.5rem;
  }
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.submit-button:active:not(:disabled) {
  transform: translateY(-2px) scale(1.01);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
}

/* Results Page Styles */
.results-card {
  background: #ffffff;
  margin-bottom: 2.5rem;
  max-width: 1200px;
}

.back-button {
  padding: 0.75rem 1.25rem;
  font-weight: 600;
  border-radius: 0.75rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.back-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Stats section styling for results page */
.stats-section {
  margin-bottom: 3rem;
}

.stat-card {
  background: #f8f9fa;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

@media (min-width: 768px) {
  .stat-card {
    margin-bottom: 1.5rem;
    padding: 2rem;
  }
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  border-color: #dee2e6;
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 1.25rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.stat-icon {
  font-size: 2rem;
  color: #0d6efd;
}

.stat-header h6 {
  margin: 0;
  font-weight: 700;
  font-size: 1.2rem;
  color: #2c3e50;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  background: #ffffff;
  border-radius: 0.75rem;
  border: 1px solid #f1f3f5;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: #f8f9fa;
  border-color: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.stat-item.highlight {
  background: #e9ecef;
  font-weight: 600;
  border-left: 4px solid #0d6efd;
}

/* Transaction type indicators */
.transaction-type {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.transaction-type.airtime {
  background-color: #cff4fc;
  color: #055160;
}

.transaction-type.data {
  background-color: #d1e7dd;
  color: #0f5132;
}

.stat-label {
  color: #495057;
  font-size: 1rem;
  font-weight: 500;
}

.stat-value {
  font-weight: 700;
  font-size: 1.1rem;
  color: #2c3e50;
  padding: 0.35rem 0.75rem;
  background-color: #f8f9fa;
  border-radius: 0.5rem;
}

.download-card {
  background: #f8f9fa;
  border-radius: 1rem;
  padding: 2rem;
  border: 1px solid #e9ecef;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.download-buttons {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

.download-button {
  flex: 1;
  min-width: 300px;
  padding: 1.25rem;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: 2px solid #0d6efd;
}

.download-button:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(13, 110, 253, 0.2);
  background-color: #0d6efd;
  color: #ffffff;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .stats-section {
    margin-bottom: 2rem;
  }

  .stat-card {
    margin-bottom: 1.5rem;
  }

  .download-button {
    width: 100%;
    min-width: 100%;
  }

  .stat-item {
    padding: 0.75rem 1rem;
  }

  .stat-value {
    font-size: 1rem;
    padding: 0.25rem 0.5rem;
  }
}

/* Status Colors */
.text-success {
  color: #198754 !important;
}

.text-warning {
  color: #ffc107 !important;
}

.text-danger {
  color: #dc3545 !important;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .form-control-custom {
    background-color: #2d2d2d;
    border-color: #3d3d3d;
    color: #ffffff;
  }

  .form-control-custom::placeholder {
    color: #6c757d;
  }

  .dark-theme .stat-card {
    background-color: #2a2a2a;
    border-color: #3d3d3d;
  }

  .dark-theme .stat-card h4 {
    color: #ffffff;
    border-bottom-color: #444;
  }

  .dark-theme .download-links {
    background-color: #2a2a2a;
    border-color: #3d3d3d;
  }

  .dark-theme .download-links h4 {
    color: #ffffff;
    border-bottom-color: #444;
  }
}

/* Adjust container padding */
.container {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

@media (min-width: 768px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

/* Professional UI Enhancements */

/* Smooth transitions for all interactive elements */
.btn,
.card,
.form-control,
.form-select {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Professional Hover Card Effects */
.hover-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.hover-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.hover-card:hover::before {
  opacity: 1;
}

.hover-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Enhanced Button Hover Effects */
.btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.btn:active:not(:disabled) {
  transform: translateY(-1px);
  transition: all 0.1s ease;
}

/* Professional Card Effects */
.card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  position: relative;
}

.card:hover {
  transform: translateY(-6px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

/* Form control enhancements */
.form-control:focus,
.form-select:focus {
  border-color: #42a5f5;
  box-shadow: 0 0 0 0.25rem rgba(66, 165, 245, 0.15);
  transform: translateY(-1px);
}

/* Icon animations */
.bi {
  transition: all 0.3s ease;
}

.btn:hover .bi {
  transform: scale(1.1);
}

/* Loading spinner enhancement */
.spinner-border {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Professional gradient overlays */
.gradient-overlay {
  position: relative;
  overflow: hidden;
}

.gradient-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.gradient-overlay:hover::before {
  left: 100%;
}

/* Enhanced table styling */
.table {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.table thead th {
  background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 50%, #1565c0 100%);
  color: white;
  border: none;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
}

.table tbody tr {
  transition: all 0.3s ease;
}

.table tbody tr:hover {
  background-color: rgba(66, 165, 245, 0.05);
  transform: scale(1.01);
}

/* Professional badge styling */
.badge {
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
}

/* Enhanced modal styling */
.modal-content {
  border: none;
  border-radius: 1rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

.modal-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem 1rem 0 0;
}

.modal-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0 0 1rem 1rem;
}

/* Professional alert styling */
.alert {
  border: none;
  border-radius: 0.75rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
}

/* Smooth page transitions */
.page-transition {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Professional Scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  margin: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  background-clip: content-box;
}

/* Professional Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 8px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}

.spinner-professional {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(102, 126, 234, 0.2);
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Professional Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-success {
  background: linear-gradient(135deg, rgba(72, 187, 120, 0.1), rgba(56, 178, 172, 0.1));
  color: #2f855a;
  border: 1px solid rgba(72, 187, 120, 0.3);
}

.status-warning {
  background: linear-gradient(135deg, rgba(237, 137, 54, 0.1), rgba(246, 173, 85, 0.1));
  color: #c05621;
  border: 1px solid rgba(237, 137, 54, 0.3);
}

.status-error {
  background: linear-gradient(135deg, rgba(245, 101, 101, 0.1), rgba(229, 62, 62, 0.1));
  color: #c53030;
  border: 1px solid rgba(245, 101, 101, 0.3);
}

.status-info {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.3);
}

/* Text selection styling */
::selection {
  background: rgba(66, 165, 245, 0.3);
  color: #1565c0;
}

/* Focus outline enhancement */
*:focus {
  outline: 2px solid rgba(66, 165, 245, 0.5);
  outline-offset: 2px;
}

/* Enhanced form styling */
.form-group-enhanced {
  position: relative;
  z-index: 1;
}

.form-control-custom:focus,
.form-select:focus {
  border-color: #1565c0 !important;
  box-shadow: 0 0 0 0.25rem rgba(66, 165, 245, 0.25) !important;
  transform: translateY(-2px);
  background-color: rgba(255, 255, 255, 1) !important;
}

.form-control-custom:hover:not(:disabled),
.form-select:hover:not(:disabled) {
  border-color: #1565c0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(66, 165, 245, 0.2);
}

/* Enhanced button hover effects */
.btn-primary:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 25px rgba(66, 165, 245, 0.4) !important;
}

.btn-primary:active {
  transform: translateY(-1px) !important;
}

/* Card body enhancements */
.card .card-body {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
}

/* Form label enhancements */
.form-label {
  font-weight: 600;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
}

/* Professional Responsive Design */
@media (max-width: 768px) {
  .form-container {
    padding: 1rem;
  }

  .card-body {
    padding: 1.5rem !important;
  }

  .btn-lg {
    height: 3.5rem !important;
    font-size: 1.1rem !important;
  }

  .icon-circle {
    width: 60px;
    height: 60px;
  }

  .icon-circle i {
    font-size: 1.5rem;
  }

  .form-section {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .file-upload-card {
    padding: 1.5rem;
    margin-bottom: 1rem;
  }

  .upload-box {
    padding: 1.5rem 1rem;
  }

  .submit-button {
    height: 3.5rem;
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .form-container {
    padding: 0.5rem;
  }

  .card {
    margin: 0.5rem;
    border-radius: 16px;
  }

  .card-body {
    padding: 1rem !important;
  }

  .form-section {
    padding: 1rem;
    border-radius: 12px;
  }

  .file-upload-card {
    padding: 1rem;
    border-radius: 12px;
  }

  .upload-box {
    padding: 1rem;
    border-radius: 12px;
  }

  .submit-button {
    height: 3rem;
    font-size: 1rem;
    border-radius: 12px;
  }
}

/* Professional Utility Classes */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.shadow-professional {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.border-gradient {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
    linear-gradient(135deg, #667eea 0%, #764ba2 100%) border-box;
}

.backdrop-blur {
  backdrop-filter: blur(20px);
}

/* Professional Focus States */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
  border-color: #667eea;
}

/* Professional Transitions */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-transform {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-colors {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* Additional Professional Enhancements */
.text-gradient-white {
  background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.9) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.upload-label {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  background: none;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.upload-label:hover {
  transform: translateY(-2px);
}

/* Enhanced Card Body Styling */
.card .card-body {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
}

/* Professional Badge Enhancements */
.badge-professional {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.badge-success-professional {
  background: linear-gradient(135deg, rgba(72, 187, 120, 0.1), rgba(56, 178, 172, 0.1));
  color: #2f855a;
  border: 1px solid rgba(72, 187, 120, 0.3);
}

.badge-warning-professional {
  background: linear-gradient(135deg, rgba(237, 137, 54, 0.1), rgba(246, 173, 85, 0.1));
  color: #c05621;
  border: 1px solid rgba(237, 137, 54, 0.3);
}

.badge-info-professional {
  background: linear-gradient(135deg, rgba(66, 153, 225, 0.1), rgba(99, 179, 237, 0.1));
  color: #2b6cb0;
  border: 1px solid rgba(66, 153, 225, 0.3);
}

/* Professional Table Enhancements */
.table-professional {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  background-color: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
}

.table-professional thead th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 700;
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 0.5px;
  padding: 1rem 1.5rem;
  border: none;
}

.table-professional tbody tr {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
}

.table-professional tbody tr:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
  transform: scale(1.01);
}

.table-professional tbody td {
  padding: 1rem 1.5rem;
  border-color: rgba(226, 232, 240, 0.6);
  vertical-align: middle;
  font-weight: 500;
}

/* Enhanced Form Select Styling for Headers */
.form-select-custom {
  background-color: rgba(255, 255, 255, 0.95) !important;
  border: 2px solid rgba(255, 255, 255, 0.8) !important;
  color: white !important;
  font-weight: 600 !important;
  font-size: 0.95rem !important;
  border-radius: 25px !important;
  padding: 0.5rem 1rem !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.form-select-custom:focus {
  border-color: rgba(255, 255, 255, 1) !important;
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
  background-color: rgba(255, 255, 255, 1) !important;
  color: #2d3748 !important;
}

.form-select-custom option {
  color: #2d3748 !important;
  background-color: white !important;
  font-weight: 500 !important;
}

/* Professional Upload Box Enhancements */
.upload-box {
  min-height: 120px;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 16px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.upload-box:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.08));
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.upload-icon {
  font-size: 2rem;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.upload-text {
  font-weight: 600;
  color: #4a5568;
  font-size: 1rem;
}

.selected-file {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.selected-file::before {
  content: "✓";
  font-weight: bold;
}