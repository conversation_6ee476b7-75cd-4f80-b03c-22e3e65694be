import React, { useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON>, Card, Col, Container, Form, Modal, Row, Table, Badge, Spinner } from 'react-bootstrap';
import axios from 'axios';

type SourceKey = 'hisa-one' | 'hisa-two';

interface WalletItem {
  id?: number | string;
  user_id?: number | string;
  amount?: number | string;
  user?: { id?: number | string; name?: string; email?: string };
  [k: string]: any;
}

interface ReconciliationData {
  opening_balance: number;
  closing_balance: number;
  total_debit: number;
  total_credit: number;
  expected_closing_balance: number;
  difference: number;
  balanced: boolean;
}

interface Props {
  baseURL: string;
  token: string;
}

const extractWallets = (apiResp: any): WalletItem[] => {
  if (!apiResp) return [];
  const data = apiResp.data; // backend wraps in { status_code, status, data, ... }
  if (Array.isArray(data)) return data;
  if (data && Array.isArray(data.data)) return data.data;
  return [];
};

const extractOverallBalance = (apiResp: any): number => {
  return typeof apiResp?.overall_balance === 'number' ? apiResp.overall_balance : 0;
};

const WalletsDashboard: React.FC<Props> = ({ baseURL, token }) => {
  const [loading, setLoading] = useState(false);
  const [source, setSource] = useState<SourceKey>('hisa-one');
  const [searchTerm, setSearchTerm] = useState('');

  const [walletsOne, setWalletsOne] = useState<WalletItem[]>([]);
  const [walletsTwo, setWalletsTwo] = useState<WalletItem[]>([]);
  const [overallOne, setOverallOne] = useState<number>(0);
  const [overallTwo, setOverallTwo] = useState<number>(0);

  const [filterType, setFilterType] = useState<string>('today');
  const [fromDate, setFromDate] = useState<string>('');
  const [toDate, setToDate] = useState<string>('');

  const [reconLoading, setReconLoading] = useState<boolean>(false);
  const [reconResult, setReconResult] = useState<ReconciliationData | null>(null);
  const [reconUser, setReconUser] = useState<{ id?: string | number; name?: string; email?: string } | null>(null);
  const [showReconModal, setShowReconModal] = useState(false);

  const headers = useMemo(() => ({ Authorization: `Bearer ${token}` }), [token]);

  const fetchWallets = async () => {
    setLoading(true);
    try {
      const [one, two] = await Promise.all([
        axios.get(`${baseURL}/hisa-one/wallets`, { headers }),
        axios.get(`${baseURL}/hisa-two/wallets`, { headers }),
      ]);
      setWalletsOne(extractWallets(one.data));
      setWalletsTwo(extractWallets(two.data));
      setOverallOne(extractOverallBalance(one.data));
      setOverallTwo(extractOverallBalance(two.data));
    } catch (e) {
      console.error('Error fetching wallets:', e);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWallets();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const allItems = source === 'hisa-one' ? walletsOne : walletsTwo;
  // const overall = source === 'hisa-one' ? overallOne : overallTwo; // Not used currently

  // Filter items based on search term
  const items = useMemo(() => {
    if (!searchTerm.trim()) return allItems;

    const term = searchTerm.toLowerCase();
    return allItems.filter(item => {
      const user = item.user || {};
      const name = (user as any).name || '';
      const email = (user as any).email || '';
      const userId = String((user as any).id || item.user_id || '');

      return (
        name.toLowerCase().includes(term) ||
        email.toLowerCase().includes(term) ||
        userId.toLowerCase().includes(term)
      );
    });
  }, [allItems, searchTerm]);

  const handleOpenRecon = (item: WalletItem) => {
    const u = item.user || { id: item.user_id, name: undefined, email: undefined };
    setReconUser({ id: u?.id ?? item.user_id, name: u?.name, email: u?.email });
    setFilterType('today');
    setFromDate('');
    setToDate('');
    setReconResult(null);
    setShowReconModal(true);
  };

  const handleReconcile = async () => {
    if (!reconUser?.id) return;
    setReconLoading(true);
    try {
      const params: Record<string, string> = { filter_type: filterType };
      if (filterType === 'custom') {
        if (!fromDate || !toDate) {
          setReconLoading(false);
          return;
        }
        params.from_date = fromDate;
        params.to_date = toDate;
      }
      const path = source === 'hisa-one' ? 'hisa-one' : 'hisa-two';
      const url = `${baseURL}/${path}/users/${reconUser.id}/wallet-reconciliation`;
      const { data } = await axios.get(url, { headers, params });
      const d = data?.data || {};
      setReconResult({
        opening_balance: d.opening_balance ?? 0,
        closing_balance: d.closing_balance ?? 0,
        total_debit: d.total_debit ?? 0,
        total_credit: d.total_credit ?? 0,
        expected_closing_balance: d.expected_closing_balance ?? 0,
        difference: d.difference ?? 0,
        balanced: !!d.balanced,
      });
    } catch (e) {
      console.error('Error reconciling wallet:', e);
    } finally {
      setReconLoading(false);
    }
  };

  return (
    <div className="form-container page-transition">
      <Container fluid className="px-4">
        {/* Header Section */}
        <Row className="mb-4">
          <Col>
            <Card className="shadow-lg border-0 rounded-4">
              <Card.Header
                className="text-white p-4 rounded-top-4 border-0"
                style={{
                  background: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 50%, #1565c0 100%)'
                }}
              >
                <div className="d-flex justify-content-between align-items-center">
                  <div>
                    <h3 className="mb-0 fw-bold">
                      <i className="bi bi-wallet2 me-2"></i>
                      Wallet Management Dashboard
                    </h3>
                    <p className="mb-0 mt-2 opacity-75 fw-light">Monitor and reconcile user wallet balances</p>
                  </div>
                  <div className="d-flex gap-3">
                    <Form.Select
                      value={source}
                      onChange={(e) => setSource(e.target.value as SourceKey)}
                      className="form-select-custom focus-ring"
                      style={{
                        minWidth: '160px',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        border: '2px solid rgba(255, 255, 255, 0.8)',
                        color: 'white',
                        fontWeight: '600',
                        fontSize: '0.95rem'
                      }}
                    >
                      <option value="hisa-one" style={{ color: '#2d3748' }}>HISA One</option>
                      <option value="hisa-two" style={{ color: '#2d3748' }}>HISA Two</option>
                    </Form.Select>
                    <Button
                      variant="outline-light"
                      onClick={fetchWallets}
                      disabled={loading}
                      className="rounded-pill px-3"
                    >
                      {loading ? <Spinner size="sm" /> : <><i className="bi bi-arrow-clockwise me-2"></i>Refresh</>}
                    </Button>
                  </div>
                </div>
              </Card.Header>
            </Card>
          </Col>
        </Row>

        {/* Statistics Cards */}
        <Row className="mb-4 g-4">
          <Col xl={4} lg={6} md={6}>
            <Card className="h-100 shadow-sm border-0 rounded-3 hover-card">
              <Card.Body className="p-4">
                <div className="d-flex align-items-center">
                  <div className="icon-circle bg-primary bg-opacity-10 me-3">
                    <i className="bi bi-bank fs-2 text-primary"></i>
                  </div>
                  <div>
                    <h6 className="text-muted mb-1 fw-semibold">HISA One Total</h6>
                    <h4 className="mb-0 fw-bold text-primary">₦{overallOne.toLocaleString()}</h4>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>
          <Col xl={4} lg={6} md={6}>
            <Card className="h-100 shadow-sm border-0 rounded-3 hover-card">
              <Card.Body className="p-4">
                <div className="d-flex align-items-center">
                  <div className="icon-circle bg-info bg-opacity-10 me-3">
                    <i className="bi bi-bank2 fs-2 text-info"></i>
                  </div>
                  <div>
                    <h6 className="text-muted mb-1 fw-semibold">HISA Two Total</h6>
                    <h4 className="mb-0 fw-bold text-info">₦{overallTwo.toLocaleString()}</h4>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>
          <Col xl={4} lg={12} md={12}>
            <Card className="h-100 shadow-sm border-0 rounded-3 hover-card">
              <Card.Body className="p-4">
                <div className="d-flex align-items-center">
                  <div className="icon-circle bg-primary bg-opacity-10 me-3">
                    <i className="bi bi-people fs-2 text-primary"></i>
                  </div>
                  <div>
                    <h6 className="text-muted mb-1 fw-semibold">Active Users ({source === 'hisa-one' ? 'HISA One' : 'HISA Two'})</h6>
                    <h4 className="mb-0 fw-bold text-primary">{items.length} users</h4>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Users Table */}
        <Card className="shadow-lg border-0 rounded-4">
          <Card.Header className="bg-light border-0 p-4">
            <Row className="align-items-center">
              <Col md={6}>
                <h5 className="mb-0 fw-bold text-dark">
                  <i className="bi bi-people me-2 text-primary"></i>
                  Users and Current Balances
                </h5>
                <small className="text-muted">{items.length} of {allItems.length} users shown</small>
              </Col>
              <Col md={6}>
                <div className="position-relative">
                  <Form.Control
                    type="text"
                    placeholder="Search by name, email, or user ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="rounded-pill ps-5"
                    style={{ height: '45px' }}
                  />
                  <i className="bi bi-search position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                  {searchTerm && (
                    <Button
                      variant="link"
                      size="sm"
                      className="position-absolute top-50 end-0 translate-middle-y me-2 p-0"
                      onClick={() => setSearchTerm('')}
                    >
                      <i className="bi bi-x-circle text-muted"></i>
                    </Button>
                  )}
                </div>
              </Col>
            </Row>
          </Card.Header>
          <Card.Body className="p-0">
            {items.length === 0 ? (
              <div className="text-center py-5">
                <i className="bi bi-search fs-1 text-muted mb-3 d-block"></i>
                <h5 className="text-muted">No users found</h5>
                <p className="text-muted mb-0">
                  {searchTerm ? 'Try adjusting your search terms' : 'No users available in this source'}
                </p>
              </div>
            ) : (
              <Table hover responsive className="mb-0">
                <thead className="bg-light">
                  <tr>
                    <th className="border-0 fw-semibold text-dark">User</th>
                    <th className="border-0 fw-semibold text-dark">Email</th>
                    <th className="border-0 fw-semibold text-dark">User ID</th>
                    <th className="border-0 fw-semibold text-dark text-end">Current Balance</th>
                    <th className="border-0 fw-semibold text-dark">Action</th>
                  </tr>
                </thead>
                <tbody>
                  {items.map((w, idx) => {
                    const u = w.user || {};
                    const uid = (u as any).id ?? w.user_id;
                    const name = (u as any).name || '-';
                    const email = (u as any).email || '-';
                    const amount = typeof w.amount === 'number' ? w.amount : parseInt(String(w.amount || 0));
                    return (
                      <tr key={`${uid}-${idx}`}>
                        <td>{name}</td>
                        <td>{email}</td>
                        <td><Badge bg="secondary">{uid}</Badge></td>
                        <td className="text-end">NGN {isNaN(amount) ? 0 : amount.toLocaleString()}</td>
                        <td>
                          <Button size="sm" variant="primary" onClick={() => handleOpenRecon(w)}>
                            Reconcile Wallet
                          </Button>
                        </td>
                      </tr>
                    );
                  })}
                  {items.length === 0 && (
                    <tr>
                      <td colSpan={5} className="text-center text-muted py-4">No data</td>
                    </tr>
                  )}
                </tbody>
              </Table>
            )}
          </Card.Body>
        </Card>

        {/* Reconciliation Modal */}
        <Modal show={showReconModal} onHide={() => setShowReconModal(false)}>
          <Modal.Header closeButton>
            <Modal.Title>Wallet Reconciliation</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div className="mb-2">
              <div className="small text-muted">User</div>
              <div className="fw-semibold">{reconUser?.name || reconUser?.email || reconUser?.id}</div>
            </div>
            <Row className="g-3">
              <Col md={6}>
                <Form.Group>
                  <Form.Label>Filter Type</Form.Label>
                  <Form.Select value={filterType} onChange={(e) => setFilterType(e.target.value)}>
                    <option value="today">Today</option>
                    <option value="yesterday">Yesterday</option>
                    <option value="current_week">Current Week</option>
                    <option value="last_week">Last Week</option>
                    <option value="current_month">Current Month</option>
                    <option value="last_month">Last Month</option>
                    <option value="current_quarter">Current Quarter</option>
                    <option value="last_quarter">Last Quarter</option>
                    <option value="custom">Custom</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              {filterType === 'custom' && (
                <>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>From Date</Form.Label>
                      <Form.Control type="date" value={fromDate} onChange={(e) => setFromDate(e.target.value)} />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>To Date</Form.Label>
                      <Form.Control type="date" value={toDate} onChange={(e) => setToDate(e.target.value)} />
                    </Form.Group>
                  </Col>
                </>
              )}
            </Row>

            {reconResult && (
              <Card className="mt-3">
                <Card.Body>
                  <Row>
                    <Col md={6}>Opening: <strong>NGN {reconResult.opening_balance.toLocaleString()}</strong></Col>
                    <Col md={6}>Closing: <strong>NGN {reconResult.closing_balance.toLocaleString()}</strong></Col>
                    <Col md={6} className="mt-2">Debit: <strong className="text-danger">NGN {reconResult.total_debit.toLocaleString()}</strong></Col>
                    <Col md={6} className="mt-2">Credit: <strong className="text-success">NGN {reconResult.total_credit.toLocaleString()}</strong></Col>
                    <Col md={12} className="mt-2">Expected Closing: <strong>NGN {reconResult.expected_closing_balance.toLocaleString()}</strong></Col>
                    <Col md={12} className="mt-2">Spotted Difference: <strong className={reconResult.difference === 0 ? 'text-success' : 'text-danger'}>NGN {reconResult.difference.toLocaleString()}</strong></Col>
                    <Col md={12} className="mt-2">Balanced: {reconResult.balanced ? <Badge bg="success">Yes</Badge> : <Badge bg="danger">No</Badge>}</Col>
                  </Row>
                </Card.Body>
              </Card>
            )}
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowReconModal(false)}>Close</Button>
            <Button variant="primary" onClick={handleReconcile} disabled={reconLoading}>
              {reconLoading ? <Spinner size="sm" /> : 'Reconcile'}
            </Button>
          </Modal.Footer>
        </Modal>
      </Container>

      <style>{`
        .hover-card {
          transition: all 0.3s ease;
        }
        .hover-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        }
        .icon-circle {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .form-container {
          min-height: 100vh;
          padding: 2rem 0;
        }
        @media (max-width: 768px) {
          .icon-circle {
            width: 50px;
            height: 50px;
          }
        }
      `}</style>
    </div>
  );
};

export default WalletsDashboard;

