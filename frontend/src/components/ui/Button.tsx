/**
 * Enhanced <PERSON>ton Component
 * 
 * Sophisticated button component with multiple variants, sizes,
 * loading states, and accessibility features.
 */

import React, { forwardRef, ButtonHTMLAttributes } from 'react';
import { Spinner } from 'react-bootstrap';
import { motion, HTMLMotionProps } from 'framer-motion';
import classNames from 'classnames';

// Button variants and sizes
type ButtonVariant = 
  | 'primary' 
  | 'secondary' 
  | 'success' 
  | 'danger' 
  | 'warning' 
  | 'info' 
  | 'light' 
  | 'dark'
  | 'outline-primary'
  | 'outline-secondary'
  | 'gradient-primary'
  | 'gradient-success'
  | 'glass'
  | 'minimal';

type ButtonSize = 'sm' | 'md' | 'lg' | 'xl';

interface ButtonProps extends Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'size'> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  loading?: boolean;
  loadingText?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  rounded?: boolean;
  elevated?: boolean;
  animated?: boolean;
  pulse?: boolean;
  children: React.ReactNode;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(({
  variant = 'primary',
  size = 'md',
  loading = false,
  loadingText,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  rounded = false,
  elevated = false,
  animated = true,
  pulse = false,
  disabled,
  className,
  children,
  ...props
}, ref) => {
  const isDisabled = disabled || loading;

  const buttonClasses = classNames(
    'btn-enhanced',
    `btn-enhanced--${variant}`,
    `btn-enhanced--${size}`,
    {
      'btn-enhanced--full-width': fullWidth,
      'btn-enhanced--rounded': rounded,
      'btn-enhanced--elevated': elevated,
      'btn-enhanced--loading': loading,
      'btn-enhanced--pulse': pulse && !loading,
      'btn-enhanced--disabled': isDisabled,
    },
    className
  );

  const motionProps: HTMLMotionProps<"button"> = animated ? {
    whileHover: isDisabled ? {} : { scale: 1.02, y: -1 },
    whileTap: isDisabled ? {} : { scale: 0.98 },
    transition: { type: "spring", stiffness: 400, damping: 17 }
  } : {};

  const renderContent = () => {
    if (loading) {
      return (
        <div className="btn-enhanced__content">
          <Spinner
            as="span"
            animation="border"
            size="sm"
            role="status"
            aria-hidden="true"
            className="btn-enhanced__spinner"
          />
          {loadingText && <span className="btn-enhanced__loading-text">{loadingText}</span>}
        </div>
      );
    }

    return (
      <div className="btn-enhanced__content">
        {icon && iconPosition === 'left' && (
          <span className="btn-enhanced__icon btn-enhanced__icon--left">
            {icon}
          </span>
        )}
        <span className="btn-enhanced__text">{children}</span>
        {icon && iconPosition === 'right' && (
          <span className="btn-enhanced__icon btn-enhanced__icon--right">
            {icon}
          </span>
        )}
      </div>
    );
  };

  return (
    <motion.button
      ref={ref}
      className={buttonClasses}
      disabled={isDisabled}
      {...motionProps}
      {...props}
    >
      {renderContent()}
    </motion.button>
  );
});

Button.displayName = 'Button';

export { Button };
export type { ButtonProps, ButtonVariant, ButtonSize };
