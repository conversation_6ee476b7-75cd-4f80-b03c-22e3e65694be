/**
 * Enhanced Card Component
 * 
 * Sophisticated card component with glassmorphism effects,
 * animations, and flexible layout options.
 */

import React, { forwardRef, HTMLAttributes } from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import classNames from 'classnames';

type CardVariant = 'default' | 'elevated' | 'glass' | 'gradient' | 'minimal' | 'bordered';
type CardSize = 'sm' | 'md' | 'lg' | 'xl';

interface CardProps extends HTMLAttributes<HTMLDivElement> {
  variant?: CardVariant;
  size?: CardSize;
  hoverable?: boolean;
  animated?: boolean;
  loading?: boolean;
  children: React.ReactNode;
}

interface CardHeaderProps extends HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  actions?: React.ReactNode;
}

interface CardBodyProps extends HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

interface CardFooterProps extends HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  bordered?: boolean;
}

const Card = forwardRef<HTMLDivElement, CardProps>(({
  variant = 'default',
  size = 'md',
  hoverable = false,
  animated = true,
  loading = false,
  className,
  children,
  ...props
}, ref) => {
  const cardClasses = classNames(
    'card-enhanced',
    `card-enhanced--${variant}`,
    `card-enhanced--${size}`,
    {
      'card-enhanced--hoverable': hoverable,
      'card-enhanced--loading': loading,
    },
    className
  );

  const motionProps: HTMLMotionProps<"div"> = animated ? {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.3, ease: "easeOut" },
    ...(hoverable && {
      whileHover: { 
        y: -4, 
        boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
        transition: { duration: 0.2 }
      }
    })
  } : {};

  if (loading) {
    return (
      <motion.div
        ref={ref}
        className={cardClasses}
        {...motionProps}
        {...props}
      >
        <div className="card-enhanced__loading">
          <div className="card-enhanced__skeleton">
            <div className="skeleton-line skeleton-line--title"></div>
            <div className="skeleton-line skeleton-line--text"></div>
            <div className="skeleton-line skeleton-line--text"></div>
            <div className="skeleton-line skeleton-line--button"></div>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      ref={ref}
      className={cardClasses}
      {...motionProps}
      {...props}
    >
      {children}
    </motion.div>
  );
});

const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(({
  children,
  actions,
  className,
  ...props
}, ref) => {
  return (
    <div
      ref={ref}
      className={classNames('card-enhanced__header', className)}
      {...props}
    >
      <div className="card-enhanced__header-content">
        {children}
      </div>
      {actions && (
        <div className="card-enhanced__header-actions">
          {actions}
        </div>
      )}
    </div>
  );
});

const CardBody = forwardRef<HTMLDivElement, CardBodyProps>(({
  children,
  padding = 'md',
  className,
  ...props
}, ref) => {
  return (
    <div
      ref={ref}
      className={classNames(
        'card-enhanced__body',
        `card-enhanced__body--padding-${padding}`,
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
});

const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>(({
  children,
  bordered = true,
  className,
  ...props
}, ref) => {
  return (
    <div
      ref={ref}
      className={classNames(
        'card-enhanced__footer',
        {
          'card-enhanced__footer--bordered': bordered,
        },
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
});

// Set display names
Card.displayName = 'Card';
CardHeader.displayName = 'CardHeader';
CardBody.displayName = 'CardBody';
CardFooter.displayName = 'CardFooter';

export { Card, CardHeader, CardBody, CardFooter };
export type { CardProps, CardHeaderProps, CardBodyProps, CardFooterProps };
