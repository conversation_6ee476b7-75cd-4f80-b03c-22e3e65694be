/**
 * Stats Card Component
 * 
 * Animated statistics card with trend indicators and
 * sophisticated visual design.
 */

import React from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, TrendingDown } from 'lucide-react';
import { Card, CardBody } from '../ui/Card';

interface StatsCardProps {
  title: string;
  value: string | number;
  trend?: number;
  icon?: React.ReactNode;
  color?: 'primary' | 'success' | 'danger' | 'warning' | 'info';
  loading?: boolean;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  trend,
  icon,
  color = 'primary',
  loading = false
}) => {
  const getTrendColor = (trendValue?: number) => {
    if (!trendValue) return 'text-muted';
    return trendValue > 0 ? 'text-success' : 'text-danger';
  };

  const getTrendIcon = (trendValue?: number) => {
    if (!trendValue) return null;
    return trendValue > 0 ? <TrendingUp size={16} /> : <TrendingDown size={16} />;
  };

  const cardVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    }
  };

  const valueVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        delay: 0.2,
        duration: 0.5
      }
    }
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover={{ y: -4, transition: { duration: 0.2 } }}
    >
      <Card 
        variant="elevated" 
        hoverable 
        loading={loading}
        className={`stats-card stats-card--${color}`}
      >
        <CardBody padding="lg">
          <div className="stats-card__content">
            {/* Header */}
            <div className="stats-card__header">
              <div className="stats-card__title">
                {title}
              </div>
              {icon && (
                <div className={`stats-card__icon stats-card__icon--${color}`}>
                  {icon}
                </div>
              )}
            </div>

            {/* Value */}
            <motion.div 
              className="stats-card__value"
              variants={valueVariants}
            >
              {value}
            </motion.div>

            {/* Trend */}
            {trend !== undefined && (
              <div className={`stats-card__trend ${getTrendColor(trend)}`}>
                <span className="stats-card__trend-icon">
                  {getTrendIcon(trend)}
                </span>
                <span className="stats-card__trend-value">
                  {Math.abs(trend)}%
                </span>
                <span className="stats-card__trend-label">
                  vs last period
                </span>
              </div>
            )}
          </div>

          {/* Background Pattern */}
          <div className="stats-card__pattern">
            <svg
              width="100"
              height="100"
              viewBox="0 0 100 100"
              className="stats-card__pattern-svg"
            >
              <defs>
                <pattern
                  id={`pattern-${color}`}
                  x="0"
                  y="0"
                  width="20"
                  height="20"
                  patternUnits="userSpaceOnUse"
                >
                  <circle cx="10" cy="10" r="1" fill="currentColor" opacity="0.1" />
                </pattern>
              </defs>
              <rect width="100" height="100" fill={`url(#pattern-${color})`} />
            </svg>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export { StatsCard };
