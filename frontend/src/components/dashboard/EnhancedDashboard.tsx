/**
 * Enhanced Dashboard Component
 * 
 * Sophisticated dashboard with real-time updates, advanced visualizations,
 * and intuitive user workflows following modern UX patterns.
 */

import React, { useState, useEffect, useMemo } from 'react';
import { Container, Row, Col, Navbar, Nav, Dropdown } from 'react-bootstrap';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  FileText, 
  Settings, 
  Bell,
  Search,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react';

import { useAuth } from '../../hooks/useAuth';
import { useReconciliation } from '../../hooks/useReconciliation';
import { Card, CardHeader, CardBody } from '../ui/Card';
import { Button } from '../ui/Button';
import { StatsCard } from './StatsCard';
import { ReconciliationChart } from './ReconciliationChart';
import { RecentActivity } from './RecentActivity';
import { QuickActions } from './QuickActions';
import { NotificationCenter } from './NotificationCenter';

interface DashboardProps {
  className?: string;
}

const EnhancedDashboard: React.FC<DashboardProps> = ({ className }) => {
  const { user, logout } = useAuth();
  const { history, loadHistory, isProcessing } = useReconciliation();
  
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');
  const [searchQuery, setSearchQuery] = useState('');
  const [showNotifications, setShowNotifications] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Load data on mount
  useEffect(() => {
    loadHistory();
  }, [loadHistory]);

  // Mock data for demonstration
  const dashboardStats = useMemo(() => ({
    totalReconciliations: 1247,
    successRate: 94.2,
    totalValue: 15678900,
    activeUsers: 23,
    trends: {
      reconciliations: +12.5,
      successRate: +2.1,
      totalValue: +8.7,
      activeUsers: +4
    }
  }), []);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadHistory();
      // Add other refresh operations here
    } finally {
      setRefreshing(false);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100
      }
    }
  };

  return (
    <div className={`enhanced-dashboard ${className || ''}`}>
      {/* Navigation Header */}
      <Navbar expand="lg" className="dashboard-navbar" variant="light">
        <Container fluid>
          <Navbar.Brand className="dashboard-brand">
            <BarChart3 className="brand-icon" />
            <span className="brand-text">HISA Reconciliation</span>
          </Navbar.Brand>
          
          <div className="dashboard-nav-controls">
            {/* Search */}
            <div className="search-container">
              <Search className="search-icon" />
              <input
                type="text"
                placeholder="Search reconciliations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="search-input"
              />
            </div>
            
            {/* Actions */}
            <div className="nav-actions">
              <Button
                variant="minimal"
                size="sm"
                icon={<RefreshCw className={refreshing ? 'spinning' : ''} />}
                onClick={handleRefresh}
                disabled={refreshing}
              >
                Refresh
              </Button>
              
              <Button
                variant="minimal"
                size="sm"
                icon={<Bell />}
                onClick={() => setShowNotifications(!showNotifications)}
                className="notification-toggle"
              >
                <span className="notification-badge">3</span>
              </Button>
              
              <Dropdown align="end">
                <Dropdown.Toggle as={Button} variant="minimal" size="sm">
                  <div className="user-avatar">
                    {user?.email?.charAt(0).toUpperCase()}
                  </div>
                  <span className="user-name">{user?.email}</span>
                </Dropdown.Toggle>
                
                <Dropdown.Menu className="user-menu">
                  <Dropdown.Item>
                    <Settings className="menu-icon" />
                    Settings
                  </Dropdown.Item>
                  <Dropdown.Divider />
                  <Dropdown.Item onClick={logout}>
                    Logout
                  </Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            </div>
          </div>
        </Container>
      </Navbar>

      {/* Main Content */}
      <Container fluid className="dashboard-content">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Stats Overview */}
          <motion.div variants={itemVariants}>
            <Row className="mb-4">
              <Col xl={3} lg={6} md={6} className="mb-3">
                <StatsCard
                  title="Total Reconciliations"
                  value={dashboardStats.totalReconciliations.toLocaleString()}
                  trend={dashboardStats.trends.reconciliations}
                  icon={<FileText />}
                  color="primary"
                />
              </Col>
              <Col xl={3} lg={6} md={6} className="mb-3">
                <StatsCard
                  title="Success Rate"
                  value={`${dashboardStats.successRate}%`}
                  trend={dashboardStats.trends.successRate}
                  icon={<TrendingUp />}
                  color="success"
                />
              </Col>
              <Col xl={3} lg={6} md={6} className="mb-3">
                <StatsCard
                  title="Total Value"
                  value={`₦${(dashboardStats.totalValue / 1000000).toFixed(1)}M`}
                  trend={dashboardStats.trends.totalValue}
                  icon={<BarChart3 />}
                  color="info"
                />
              </Col>
              <Col xl={3} lg={6} md={6} className="mb-3">
                <StatsCard
                  title="Active Users"
                  value={dashboardStats.activeUsers.toString()}
                  trend={dashboardStats.trends.activeUsers}
                  icon={<Users />}
                  color="warning"
                />
              </Col>
            </Row>
          </motion.div>

          {/* Charts and Analytics */}
          <Row className="mb-4">
            <Col lg={8} className="mb-3">
              <motion.div variants={itemVariants}>
                <Card variant="elevated" hoverable>
                  <CardHeader>
                    <div className="chart-header">
                      <h5 className="chart-title">Reconciliation Trends</h5>
                      <div className="chart-controls">
                        <Button
                          variant="minimal"
                          size="sm"
                          icon={<Filter />}
                        >
                          Filter
                        </Button>
                        <Button
                          variant="minimal"
                          size="sm"
                          icon={<Download />}
                        >
                          Export
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardBody>
                    <ReconciliationChart timeRange={selectedTimeRange} />
                  </CardBody>
                </Card>
              </motion.div>
            </Col>
            
            <Col lg={4} className="mb-3">
              <motion.div variants={itemVariants}>
                <Card variant="elevated" hoverable>
                  <CardHeader>
                    <h5 className="chart-title">Recent Activity</h5>
                  </CardHeader>
                  <CardBody>
                    <RecentActivity activities={history.slice(0, 5)} />
                  </CardBody>
                </Card>
              </motion.div>
            </Col>
          </Row>

          {/* Quick Actions */}
          <motion.div variants={itemVariants}>
            <Row className="mb-4">
              <Col>
                <Card variant="glass">
                  <CardHeader>
                    <h5 className="section-title">Quick Actions</h5>
                  </CardHeader>
                  <CardBody>
                    <QuickActions />
                  </CardBody>
                </Card>
              </Col>
            </Row>
          </motion.div>
        </motion.div>
      </Container>

      {/* Notification Center */}
      <AnimatePresence>
        {showNotifications && (
          <NotificationCenter
            onClose={() => setShowNotifications(false)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export { EnhancedDashboard };
