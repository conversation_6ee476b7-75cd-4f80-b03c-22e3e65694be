import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Con<PERSON>er, <PERSON>, <PERSON>, Modal, Table } from 'react-bootstrap';
import DatePicker from './DatePicker';

interface TelcoSummary {
    target_date: string;
    hisa_source: string;
    total_transactions: number;
    total_value_naira: number;
    telco_summary: {
        [telco: string]: {
            total_transactions: number;
            total_value_naira: number;
            by_status: {
                [status: string]: {
                    count: number;
                    value_naira: number;
                };
            };
            by_type: {
                [type: string]: {
                    count: number;
                    value_naira: number;
                };
            };
            reconciliation?: {
                hisa_transactions: number;
                telco_transactions: number;
                matching_transactions: number;
                hisa_only_transactions: number;
                telco_only_transactions: number;
                match_rate: number;
                hisa_value_naira: number;
                telco_value_naira: number;
                value_difference_naira: number;
            };
        };
    };
    bill_summary?: {
        [bill_type: string]: {
            total_transactions: number;
            total_value_naira: number;
            by_status: {
                [status: string]: {
                    count: number;
                    value_naira: number;
                };
            };
            category: string;
        };
    };
    telco_files_processed?: string[];
    reconciliation_available?: boolean;
    reconciliation_results?: {
        [telco: string]: any;
    };
}



interface Props {
    baseURL?: string;
    token?: string;
}

const TelcoSummaryDashboard: React.FC<Props> = ({ baseURL, token }) => {
    const [targetDate, setTargetDate] = useState('');
    const [hisaSource, setHisaSource] = useState<'hisa_one' | 'hisa_two'>('hisa_one');
    const [telcoSummary, setTelcoSummary] = useState<TelcoSummary | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [showModal, setShowModal] = useState(false);
    const [selectedTelco, setSelectedTelco] = useState<string>('');
    const [userConsumption, setUserConsumption] = useState<any[]>([]);
    const [modalLoading, setModalLoading] = useState(false);

    // File upload states
    const [mtnFiles, setMtnFiles] = useState<File[]>([]);
    const [airtelFiles, setAirtelFiles] = useState<File[]>([]);
    const [gloFiles, setGloFiles] = useState<File[]>([]);
    const [showFileUpload, setShowFileUpload] = useState(false);

    const handleViewDetails = async (telco: string) => {
        setSelectedTelco(telco);
        setShowModal(true);
        setModalLoading(true);
        setUserConsumption([]);

        try {
            const apiBaseURL = baseURL?.replace(/\/+$/, '') || 'http://localhost:8000';
            const authToken = token || localStorage.getItem('access_token') || localStorage.getItem('authToken');

            const response = await fetch(`${apiBaseURL}/telco-user-consumption/${targetDate}/${telco}?hisa_source=${hisaSource}`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                },
            });

            const data = await response.json();

            if (data.status && data.data) {
                setUserConsumption(data.data.user_consumption || []);
            } else {
                console.error('Failed to fetch user consumption:', data.error);
            }
        } catch (error) {
            console.error('Error fetching user consumption:', error);
        } finally {
            setModalLoading(false);
        }
    };

    const fetchTelcoSummary = async () => {
        if (!targetDate) {
            setError('Please select a date');
            return;
        }

        setLoading(true);
        setError(null);
        setTelcoSummary(null);

        try {
            const apiBaseURL = baseURL || (import.meta.env.DEV ? 'http://localhost:8080' : import.meta.env.VITE_API_BASE_URL);
            const authToken = token || localStorage.getItem('access_token') || localStorage.getItem('authToken');

            // Check if any files are uploaded
            const hasFiles = mtnFiles.length > 0 || airtelFiles.length > 0 || gloFiles.length > 0;

            if (hasFiles) {
                // Use enhanced endpoint with file uploads
                const formData = new FormData();
                formData.append('target_date', targetDate);
                formData.append('hisa_source', hisaSource);

                // Add MTN files
                mtnFiles.forEach((file, index) => {
                    formData.append(`mtn_file${index + 1}`, file);
                });

                // Add Airtel files
                airtelFiles.forEach((file, index) => {
                    formData.append(`airtel_file${index + 1}`, file);
                });

                // Add GLO files
                gloFiles.forEach((file, index) => {
                    formData.append(`glo_file${index + 1}`, file);
                });

                const response = await fetch(`${apiBaseURL}/telco-summary`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                    },
                    body: formData,
                });

                const data = await response.json();

                if (data.status) {
                    setTelcoSummary(data.data);
                } else {
                    setError(data.error || 'Failed to fetch enhanced telco summary');
                }
            } else {
                // Use legacy endpoint without files
                const response = await fetch(
                    `${apiBaseURL}/telco-summary/${targetDate}?hisa_source=${hisaSource}`,
                    {
                        headers: {
                            'Authorization': `Bearer ${authToken}`,
                        },
                    }
                );

                const data = await response.json();

                if (data.status) {
                    setTelcoSummary(data.data);
                } else {
                    setError(data.error || 'Failed to fetch telco summary');
                }
            }
        } catch (err) {
            setError('Network error occurred');
        } finally {
            setLoading(false);
        }
    };

    const handleFileUpload = (telco: string, files: FileList | null) => {
        if (!files) return;

        const fileArray = Array.from(files).slice(0, 3); // Limit to 3 files

        switch (telco.toUpperCase()) {
            case 'MTN':
                setMtnFiles(fileArray);
                break;
            case 'AIRTEL':
                setAirtelFiles(fileArray);
                break;
            case 'GLO':
                setGloFiles(fileArray);
                break;
        }
    };

    const removeFile = (telco: string, index: number) => {
        switch (telco.toUpperCase()) {
            case 'MTN':
                setMtnFiles(prev => prev.filter((_, i) => i !== index));
                break;
            case 'AIRTEL':
                setAirtelFiles(prev => prev.filter((_, i) => i !== index));
                break;
            case 'GLO':
                setGloFiles(prev => prev.filter((_, i) => i !== index));
                break;
        }
    };

    const clearAllFiles = () => {
        setMtnFiles([]);
        setAirtelFiles([]);
        setGloFiles([]);
    };



    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-NG', {
            style: 'currency',
            currency: 'NGN',
        }).format(amount);
    };

    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case 'successful':
                return 'success';
            case 'failed':
                return 'danger';
            case 'pending':
                return 'warning';
            default:
                return 'secondary';
        }
    };

    const getTelcoColor = (telco: string) => {
        switch (telco.toUpperCase()) {
            case 'MTN':
                return 'warning';
            case 'AIRTEL':
                return 'danger';
            case 'GLO':
                return 'success';
            default:
                return 'secondary';
        }
    };

    return (
        <div className="form-container page-transition">
            <Container fluid className="px-4">
                {/* Header Section */}
                <Row className="mb-5">
                    <Col>
                        <Card className="shadow-lg border-0 rounded-4 overflow-hidden position-relative">
                            <div className="position-absolute top-0 start-0 w-100 h-100 opacity-10">
                                <div className="d-flex justify-content-between align-items-center h-100 px-5">
                                    <i className="bi bi-graph-up fs-1"></i>
                                    <i className="bi bi-bar-chart fs-1"></i>
                                    <i className="bi bi-pie-chart fs-1"></i>
                                    <i className="bi bi-activity fs-1"></i>
                                </div>
                            </div>
                            <Card.Header
                                className="text-white p-5 rounded-top-4 border-0 text-center position-relative"
                                style={{
                                    background: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 50%, #1565c0 100%)',
                                    minHeight: '200px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center'
                                }}
                            >
                                <div className="position-relative z-index-1">
                                    <div className="icon-circle bg-white bg-opacity-20 mx-auto mb-4 d-flex align-items-center justify-content-center shadow-lg"
                                        style={{
                                            width: '100px',
                                            height: '100px',
                                            borderRadius: '50%',
                                            border: '3px solid rgba(255, 255, 255, 0.3)',
                                            backdropFilter: 'blur(10px)'
                                        }}>
                                        <i className="bi bi-graph-up-arrow fs-1 text-white"></i>
                                    </div>
                                    <h1 className="fw-bold mb-3 display-6">Telco Transaction Analytics</h1>
                                    <p className="mb-0 fs-5 opacity-90 px-4">
                                        Comprehensive analysis of transaction patterns, user consumption, and telco performance metrics
                                    </p>
                                    <div className="mt-4">
                                        <Badge bg="light" text="dark" className="me-2 px-3 py-2 rounded-pill">
                                            <i className="bi bi-lightning-charge me-1"></i>
                                            Real-time Data
                                        </Badge>
                                        <Badge bg="light" text="dark" className="me-2 px-3 py-2 rounded-pill">
                                            <i className="bi bi-shield-check me-1"></i>
                                            Secure Analytics
                                        </Badge>
                                        <Badge bg="light" text="dark" className="px-3 py-2 rounded-pill">
                                            <i className="bi bi-graph-up me-1"></i>
                                            Advanced Insights
                                        </Badge>
                                    </div>
                                </div>
                            </Card.Header>
                        </Card>
                    </Col>
                </Row>

                {/* Enhanced Controls */}
                <Card className="shadow-lg border-0 rounded-4 mb-5 position-relative overflow-hidden"
                    style={{ backgroundColor: 'rgba(255, 255, 255, 0.98)', backdropFilter: 'blur(10px)' }}>
                    <div className="position-absolute top-0 end-0 opacity-5" style={{ fontSize: '8rem', right: '-2rem', top: '-2rem' }}>
                        <i className="bi bi-gear"></i>
                    </div>
                    <Card.Header
                        className="text-white border-0 p-4 position-relative"
                        style={{
                            background: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 50%, #1565c0 100%)',
                            borderRadius: '1rem 1rem 0 0'
                        }}
                    >
                        <Row className="align-items-center">
                            <Col>
                                <div className="d-flex align-items-center">
                                    <div className="icon-circle bg-white bg-opacity-20 me-3 d-flex align-items-center justify-content-center"
                                        style={{ width: '50px', height: '50px', borderRadius: '50%' }}>
                                        <i className="bi bi-sliders text-white"></i>
                                    </div>
                                    <div>
                                        <h5 className="mb-1 fw-bold">Analysis Configuration</h5>
                                        <p className="mb-0 opacity-75 small">Configure your analysis parameters below</p>
                                    </div>
                                </div>
                            </Col>
                            <Col xs="auto">
                                <Badge bg="light" text="dark" className="px-3 py-2 rounded-pill">
                                    <i className="bi bi-cpu me-1"></i>
                                    Smart Analytics
                                </Badge>
                            </Col>
                        </Row>
                    </Card.Header>
                    <Card.Body className="p-5">
                        <Container fluid>
                            <Row className="justify-content-center">
                                <Col lg={11} xl={10}>
                                    <Row className="align-items-end g-4 mb-4">
                                        <Col lg={4} md={6} sm={12}>
                                            <div className="text-center">
                                                <DatePicker
                                                    label="Target Date"
                                                    value={targetDate}
                                                    onChange={setTargetDate}
                                                    required
                                                    size="lg"
                                                />
                                            </div>
                                        </Col>
                                        <Col lg={4} md={6} sm={12}>
                                            <div className="text-center">
                                                <Form.Group className="mb-3">
                                                    <Form.Label className="fw-semibold mb-3 d-flex align-items-center justify-content-center" style={{ color: '#1565c0', fontSize: '1rem' }}>
                                                        <div className="me-2" style={{
                                                            width: '32px',
                                                            height: '32px',
                                                            borderRadius: '8px',
                                                            background: 'linear-gradient(135deg, #4299e1 0%, #63b3ed 100%)',
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center'
                                                        }}>
                                                            <i className="bi bi-database text-white"></i>
                                                        </div>
                                                        HISA Source
                                                        <span className="text-danger ms-1">*</span>
                                                    </Form.Label>
                                                    <Form.Select
                                                        value={hisaSource}
                                                        onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setHisaSource(e.target.value as 'hisa_one' | 'hisa_two')}
                                                        className="form-control-custom focus-ring"
                                                        size="lg"
                                                        style={{
                                                            height: '3.5rem',
                                                            fontSize: '1.1rem',
                                                            borderRadius: '12px',
                                                            border: '2px solid #4299e1',
                                                            backgroundColor: 'rgba(255, 255, 255, 0.98)',
                                                            color: '#1565c0',
                                                            fontWeight: '600',
                                                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                                            textAlign: 'center',
                                                            boxShadow: '0 4px 12px rgba(66, 153, 225, 0.15)'
                                                        }}
                                                    >
                                                        <option value="hisa_one">HISA One</option>
                                                        <option value="hisa_two">HISA Two</option>
                                                    </Form.Select>
                                                </Form.Group>
                                            </div>
                                        </Col>
                                        <Col lg={4} md={12} sm={12}>
                                            <div className="text-center">
                                                <div className="mb-3">
                                                    <Button
                                                        variant="primary"
                                                        onClick={fetchTelcoSummary}
                                                        disabled={loading || !targetDate}
                                                        className="w-100 fw-semibold transition-all"
                                                        size="lg"
                                                        style={{
                                                            height: '3.5rem',
                                                            fontSize: '1.1rem',
                                                            borderRadius: '12px',
                                                            background: 'linear-gradient(135deg, #4299e1 0%, #63b3ed 100%)',
                                                            border: 'none',
                                                            transform: 'translateY(0)',
                                                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                                                        }}
                                                        onMouseEnter={(e) => {
                                                            if (!loading && targetDate) {
                                                                e.currentTarget.style.transform = 'translateY(-2px)';
                                                                e.currentTarget.style.boxShadow = '0 8px 30px rgba(66, 153, 225, 0.4)';
                                                            }
                                                        }}
                                                        onMouseLeave={(e) => {
                                                            e.currentTarget.style.transform = 'translateY(0)';
                                                            e.currentTarget.style.boxShadow = '0 6px 20px rgba(66, 153, 225, 0.3)';
                                                        }}
                                                    >
                                                        {loading ? (
                                                            <>
                                                                <Spinner
                                                                    size="sm"
                                                                    className="me-2"
                                                                    style={{ color: 'white' }}
                                                                />
                                                                Analyzing...
                                                            </>
                                                        ) : (
                                                            <>
                                                                <i className="bi bi-bar-chart-line-fill me-2"></i>
                                                                Generate Summary
                                                            </>
                                                        )}
                                                    </Button>
                                                </div>
                                            </div>
                                        </Col>
                                    </Row>
                                </Col>
                            </Row>
                        </Container>
                    </Card.Body>
                </Card>

                {/* Enhanced File Upload Section */}
                <Card className="shadow-lg border-0 rounded-4 mb-5 position-relative overflow-hidden"
                    style={{ backgroundColor: 'rgba(255, 255, 255, 0.98)', backdropFilter: 'blur(10px)' }}>
                    <div className="position-absolute top-0 end-0 opacity-5" style={{ fontSize: '6rem', right: '-1rem', top: '-1rem' }}>
                        <i className="bi bi-cloud-upload"></i>
                    </div>
                    <Card.Header
                        className="text-white border-0 p-4 position-relative"
                        style={{
                            background: 'linear-gradient(135deg, #28a745 0%, #20c997 50%, #17a2b8 100%)',
                            borderRadius: '1rem 1rem 0 0'
                        }}
                    >
                        <Row className="align-items-center">
                            <Col>
                                <div className="d-flex align-items-center">
                                    <div className="icon-circle bg-white bg-opacity-20 me-3 d-flex align-items-center justify-content-center"
                                        style={{ width: '50px', height: '50px', borderRadius: '50%' }}>
                                        <i className="bi bi-upload text-white"></i>
                                    </div>
                                    <div>
                                        <h5 className="mb-1 fw-bold">Enhanced Analysis with Telco Files</h5>
                                        <p className="mb-0 opacity-75 small">Upload telco files for detailed reconciliation analysis (Optional)</p>
                                    </div>
                                </div>
                            </Col>
                            <Col xs="auto">
                                <Button
                                    variant="light"
                                    size="sm"
                                    onClick={() => setShowFileUpload(!showFileUpload)}
                                    className="rounded-pill px-3"
                                >
                                    <i className={`bi bi-chevron-${showFileUpload ? 'up' : 'down'} me-1`}></i>
                                    {showFileUpload ? 'Hide' : 'Show'} Upload
                                </Button>
                            </Col>
                        </Row>
                    </Card.Header>

                    {showFileUpload && (
                        <Card.Body className="p-5">
                            <Container fluid>
                                <Row className="g-4">
                                    {/* MTN Upload Section */}
                                    <Col lg={4} md={6}>
                                        <div className="telco-upload-section p-4 rounded-4 h-100"
                                            style={{
                                                background: 'linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%)',
                                                border: '2px dashed #ffc107',
                                                transition: 'all 0.3s ease'
                                            }}>
                                            <div className="text-center mb-3">
                                                <div className="d-inline-flex align-items-center justify-content-center rounded-circle mb-2"
                                                    style={{ width: '60px', height: '60px', background: '#ffc107' }}>
                                                    <i className="bi bi-telephone-fill text-white fs-4"></i>
                                                </div>
                                                <h6 className="fw-bold text-warning">MTN Files</h6>
                                                <small className="text-muted">Upload up to 3 MTN files</small>
                                            </div>

                                            <Form.Group className="mb-3">
                                                <Form.Control
                                                    type="file"
                                                    multiple
                                                    accept=".xlsx,.xls,.csv"
                                                    onChange={(e) => handleFileUpload('MTN', (e.target as HTMLInputElement).files)}
                                                    className="form-control-sm"
                                                />
                                            </Form.Group>

                                            {mtnFiles.length > 0 && (
                                                <div className="uploaded-files">
                                                    {mtnFiles.map((file, index) => (
                                                        <div key={index} className="d-flex align-items-center justify-content-between bg-white rounded p-2 mb-2 shadow-sm">
                                                            <div className="d-flex align-items-center">
                                                                <i className="bi bi-file-earmark-excel text-success me-2"></i>
                                                                <small className="text-truncate" style={{ maxWidth: '120px' }}>
                                                                    {file.name}
                                                                </small>
                                                            </div>
                                                            <Button
                                                                variant="outline-danger"
                                                                size="sm"
                                                                onClick={() => removeFile('MTN', index)}
                                                                className="p-1"
                                                            >
                                                                <i className="bi bi-x"></i>
                                                            </Button>
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                    </Col>

                                    {/* Airtel Upload Section */}
                                    <Col lg={4} md={6}>
                                        <div className="telco-upload-section p-4 rounded-4 h-100"
                                            style={{
                                                background: 'linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%)',
                                                border: '2px dashed #dc3545',
                                                transition: 'all 0.3s ease'
                                            }}>
                                            <div className="text-center mb-3">
                                                <div className="d-inline-flex align-items-center justify-content-center rounded-circle mb-2"
                                                    style={{ width: '60px', height: '60px', background: '#dc3545' }}>
                                                    <i className="bi bi-telephone-fill text-white fs-4"></i>
                                                </div>
                                                <h6 className="fw-bold text-danger">Airtel Files</h6>
                                                <small className="text-muted">Upload up to 3 Airtel files</small>
                                            </div>

                                            <Form.Group className="mb-3">
                                                <Form.Control
                                                    type="file"
                                                    multiple
                                                    accept=".xlsx,.xls,.csv"
                                                    onChange={(e) => handleFileUpload('AIRTEL', (e.target as HTMLInputElement).files)}
                                                    className="form-control-sm"
                                                />
                                            </Form.Group>

                                            {airtelFiles.length > 0 && (
                                                <div className="uploaded-files">
                                                    {airtelFiles.map((file, index) => (
                                                        <div key={index} className="d-flex align-items-center justify-content-between bg-white rounded p-2 mb-2 shadow-sm">
                                                            <div className="d-flex align-items-center">
                                                                <i className="bi bi-file-earmark-excel text-success me-2"></i>
                                                                <small className="text-truncate" style={{ maxWidth: '120px' }}>
                                                                    {file.name}
                                                                </small>
                                                            </div>
                                                            <Button
                                                                variant="outline-danger"
                                                                size="sm"
                                                                onClick={() => removeFile('AIRTEL', index)}
                                                                className="p-1"
                                                            >
                                                                <i className="bi bi-x"></i>
                                                            </Button>
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                    </Col>

                                    {/* GLO Upload Section */}
                                    <Col lg={4} md={12}>
                                        <div className="telco-upload-section p-4 rounded-4 h-100"
                                            style={{
                                                background: 'linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%)',
                                                border: '2px dashed #28a745',
                                                transition: 'all 0.3s ease'
                                            }}>
                                            <div className="text-center mb-3">
                                                <div className="d-inline-flex align-items-center justify-content-center rounded-circle mb-2"
                                                    style={{ width: '60px', height: '60px', background: '#28a745' }}>
                                                    <i className="bi bi-telephone-fill text-white fs-4"></i>
                                                </div>
                                                <h6 className="fw-bold text-success">GLO Files</h6>
                                                <small className="text-muted">Upload up to 3 GLO files</small>
                                            </div>

                                            <Form.Group className="mb-3">
                                                <Form.Control
                                                    type="file"
                                                    multiple
                                                    accept=".xlsx,.xls,.csv"
                                                    onChange={(e) => handleFileUpload('GLO', (e.target as HTMLInputElement).files)}
                                                    className="form-control-sm"
                                                />
                                            </Form.Group>

                                            {gloFiles.length > 0 && (
                                                <div className="uploaded-files">
                                                    {gloFiles.map((file, index) => (
                                                        <div key={index} className="d-flex align-items-center justify-content-between bg-white rounded p-2 mb-2 shadow-sm">
                                                            <div className="d-flex align-items-center">
                                                                <i className="bi bi-file-earmark-excel text-success me-2"></i>
                                                                <small className="text-truncate" style={{ maxWidth: '120px' }}>
                                                                    {file.name}
                                                                </small>
                                                            </div>
                                                            <Button
                                                                variant="outline-danger"
                                                                size="sm"
                                                                onClick={() => removeFile('GLO', index)}
                                                                className="p-1"
                                                            >
                                                                <i className="bi bi-x"></i>
                                                            </Button>
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                    </Col>
                                </Row>

                                {(mtnFiles.length > 0 || airtelFiles.length > 0 || gloFiles.length > 0) && (
                                    <Row className="mt-4">
                                        <Col className="text-center">
                                            <Button
                                                variant="outline-secondary"
                                                onClick={clearAllFiles}
                                                className="rounded-pill px-4"
                                            >
                                                <i className="bi bi-trash me-2"></i>
                                                Clear All Files
                                            </Button>
                                        </Col>
                                    </Row>
                                )}
                            </Container>
                        </Card.Body>
                    )}
                </Card>

                {/* Error Display */}
                {error && (
                    <Alert variant="danger" className="mb-4">
                        {error}
                    </Alert>
                )}

                {/* Telco Summary */}
                {telcoSummary && (
                    <>
                        {/* Enhanced Overall Statistics */}
                        <Card className="shadow-lg border-0 rounded-4 mb-5 position-relative overflow-hidden">
                            <div className="position-absolute top-0 start-0 w-100 h-100 opacity-5">
                                <div className="d-flex justify-content-around align-items-center h-100">
                                    <i className="bi bi-graph-up-arrow" style={{ fontSize: '4rem' }}></i>
                                    <i className="bi bi-currency-exchange" style={{ fontSize: '4rem' }}></i>
                                    <i className="bi bi-broadcast" style={{ fontSize: '4rem' }}></i>
                                </div>
                            </div>
                            <Card.Header
                                className="text-white border-0 p-4 position-relative"
                                style={{
                                    background: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 50%, #1565c0 100%)',
                                    borderRadius: '1rem 1rem 0 0'
                                }}
                            >
                                <div className="d-flex align-items-center justify-content-between">
                                    <div className="d-flex align-items-center">
                                        <div className="icon-circle bg-white bg-opacity-20 me-3 d-flex align-items-center justify-content-center"
                                            style={{ width: '50px', height: '50px', borderRadius: '50%' }}>
                                            <i className="bi bi-bar-chart text-white"></i>
                                        </div>
                                        <div>
                                            <h5 className="mb-1 fw-bold">Transaction Overview</h5>
                                            <p className="mb-0 opacity-75 small">Statistics for {telcoSummary.target_date}</p>
                                        </div>
                                    </div>
                                    <Badge bg="light" text="dark" className="px-3 py-2 rounded-pill">
                                        <i className="bi bi-calendar-check me-1"></i>
                                        {telcoSummary.target_date}
                                    </Badge>
                                </div>
                            </Card.Header>
                            <Card.Body className="p-5 position-relative">
                                <Row className="text-center g-4">
                                    <Col md={4}>
                                        <div className="stats-card p-4 rounded-4 h-100 position-relative overflow-hidden"
                                            style={{
                                                background: 'linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(30, 136, 229, 0.05) 100%)',
                                                border: '2px solid rgba(66, 165, 245, 0.2)',
                                                transition: 'all 0.3s ease'
                                            }}>
                                            <div className="position-absolute top-0 end-0 opacity-10" style={{ fontSize: '3rem', right: '1rem', top: '0.5rem' }}>
                                                <i className="bi bi-graph-up-arrow"></i>
                                            </div>
                                            <div className="icon-circle bg-primary bg-opacity-10 mx-auto mb-3 d-flex align-items-center justify-content-center"
                                                style={{ width: '60px', height: '60px', borderRadius: '50%' }}>
                                                <i className="bi bi-graph-up-arrow text-primary fs-4"></i>
                                            </div>
                                            <h2 className="text-primary mb-2 fw-bold display-6">
                                                {telcoSummary.total_transactions.toLocaleString()}
                                            </h2>
                                            <p className="text-muted mb-0 fw-semibold">Total Transactions</p>
                                            <small className="text-muted opacity-75">Processed successfully</small>
                                        </div>
                                    </Col>
                                    <Col md={4}>
                                        <div className="stats-card p-4 rounded-4 h-100 position-relative overflow-hidden"
                                            style={{
                                                background: 'linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(25, 135, 84, 0.05) 100%)',
                                                border: '2px solid rgba(40, 167, 69, 0.2)',
                                                transition: 'all 0.3s ease'
                                            }}>
                                            <div className="position-absolute top-0 end-0 opacity-10" style={{ fontSize: '3rem', right: '1rem', top: '0.5rem' }}>
                                                <i className="bi bi-currency-exchange"></i>
                                            </div>
                                            <div className="icon-circle bg-success bg-opacity-10 mx-auto mb-3 d-flex align-items-center justify-content-center"
                                                style={{ width: '60px', height: '60px', borderRadius: '50%' }}>
                                                <i className="bi bi-currency-exchange text-success fs-4"></i>
                                            </div>
                                            <h2 className="text-success mb-2 fw-bold display-6">
                                                {formatCurrency(telcoSummary.total_value_naira)}
                                            </h2>
                                            <p className="text-muted mb-0 fw-semibold">Total Value</p>
                                            <small className="text-muted opacity-75">Transaction volume</small>
                                        </div>
                                    </Col>
                                    <Col md={4}>
                                        <div className="stats-card p-4 rounded-4 h-100 position-relative overflow-hidden"
                                            style={{
                                                background: 'linear-gradient(135deg, rgba(13, 202, 240, 0.1) 0%, rgba(13, 110, 253, 0.05) 100%)',
                                                border: '2px solid rgba(13, 202, 240, 0.2)',
                                                transition: 'all 0.3s ease'
                                            }}>
                                            <div className="position-absolute top-0 end-0 opacity-10" style={{ fontSize: '3rem', right: '1rem', top: '0.5rem' }}>
                                                <i className="bi bi-broadcast"></i>
                                            </div>
                                            <div className="icon-circle bg-info bg-opacity-10 mx-auto mb-3 d-flex align-items-center justify-content-center"
                                                style={{ width: '60px', height: '60px', borderRadius: '50%' }}>
                                                <i className="bi bi-broadcast text-info fs-4"></i>
                                            </div>
                                            <h2 className="text-info mb-2 fw-bold display-6">
                                                {Object.keys(telcoSummary.telco_summary).length}
                                            </h2>
                                            <p className="text-muted mb-0 fw-semibold">Active Telcos</p>
                                            <small className="text-muted opacity-75">Network providers</small>
                                        </div>
                                    </Col>
                                </Row>
                            </Card.Body>
                        </Card>

                        {/* Enhanced Telco Breakdown Header */}
                        <Card className="shadow-lg border-0 rounded-4 mb-4 position-relative overflow-hidden">
                            <div className="position-absolute top-0 start-0 w-100 h-100 opacity-5">
                                <div className="d-flex justify-content-center align-items-center h-100">
                                    <i className="bi bi-telephone" style={{ fontSize: '5rem' }}></i>
                                </div>
                            </div>
                            <Card.Header
                                className="text-white border-0 p-4 position-relative"
                                style={{
                                    background: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 50%, #1565c0 100%)',
                                    borderRadius: '1rem 1rem 0 0'
                                }}
                            >
                                <div className="d-flex align-items-center justify-content-between">
                                    <div className="d-flex align-items-center">
                                        <div className="icon-circle bg-white bg-opacity-20 me-3 d-flex align-items-center justify-content-center"
                                            style={{ width: '50px', height: '50px', borderRadius: '50%' }}>
                                            <i className="bi bi-telephone text-white"></i>
                                        </div>
                                        <div>
                                            <h5 className="mb-1 fw-bold">Telco Performance Analysis</h5>
                                            <p className="mb-0 opacity-75 small">Detailed breakdown by network provider</p>
                                        </div>
                                    </div>
                                    <div className="d-flex gap-2">
                                        <Badge bg="light" text="dark" className="px-3 py-2 rounded-pill">
                                            <i className="bi bi-graph-up me-1"></i>
                                            Live Data
                                        </Badge>
                                        <Badge bg="light" text="dark" className="px-3 py-2 rounded-pill">
                                            <i className="bi bi-shield-check me-1"></i>
                                            Verified
                                        </Badge>
                                    </div>
                                </div>
                            </Card.Header>
                        </Card>

                        {/* Enhanced Telco Cards */}
                        <Row className="g-4">
                            {Object.entries(telcoSummary.telco_summary).map(([telco, data]) => (
                                <Col lg={4} md={6} key={telco} className="mb-4">
                                    <Card className="h-100 shadow-lg border-0 rounded-4 hover-card position-relative overflow-hidden telco-card">
                                        {/* Background Pattern */}
                                        <div className="position-absolute top-0 start-0 w-100 h-100 opacity-5">
                                            <div className="d-flex justify-content-end align-items-start h-100 p-3">
                                                <i className="bi bi-telephone" style={{ fontSize: '4rem' }}></i>
                                            </div>
                                        </div>

                                        {/* Telco Brand Indicator */}
                                        <div className="position-absolute top-0 start-0 w-100 h-2"
                                            style={{
                                                background: telco.toUpperCase() === 'MTN' ? 'linear-gradient(90deg, #ffcc02, #ffd700)' :
                                                    telco.toUpperCase() === 'AIRTEL' ? 'linear-gradient(90deg, #dc3545, #ff6b6b)' :
                                                        telco.toUpperCase() === 'GLO' ? 'linear-gradient(90deg, #28a745, #20c997)' :
                                                            'linear-gradient(90deg, #6c757d, #adb5bd)'
                                            }}>
                                        </div>

                                        <Card.Header
                                            className="d-flex justify-content-between align-items-center border-0 p-4 position-relative"
                                            style={{
                                                background: 'linear-gradient(135deg, rgba(66, 165, 245, 0.08) 0%, rgba(30, 136, 229, 0.05) 50%, rgba(21, 101, 192, 0.03) 100%)',
                                                borderRadius: '1rem 1rem 0 0'
                                            }}
                                        >
                                            <div className="d-flex align-items-center">
                                                <div className="telco-icon-wrapper me-3 d-flex align-items-center justify-content-center"
                                                    style={{
                                                        width: '50px',
                                                        height: '50px',
                                                        borderRadius: '50%',
                                                        background: telco.toUpperCase() === 'MTN' ? 'linear-gradient(135deg, rgba(255, 204, 2, 0.2), rgba(255, 215, 0, 0.1))' :
                                                            telco.toUpperCase() === 'AIRTEL' ? 'linear-gradient(135deg, rgba(220, 53, 69, 0.2), rgba(255, 107, 107, 0.1))' :
                                                                telco.toUpperCase() === 'GLO' ? 'linear-gradient(135deg, rgba(40, 167, 69, 0.2), rgba(32, 201, 151, 0.1))' :
                                                                    'linear-gradient(135deg, rgba(108, 117, 125, 0.2), rgba(173, 181, 189, 0.1))',
                                                        border: '2px solid rgba(66, 165, 245, 0.2)'
                                                    }}>
                                                    <i className={`bi bi-telephone text-${getTelcoColor(telco)} fs-5`}></i>
                                                </div>
                                                <div>
                                                    <Badge bg={getTelcoColor(telco)} className="mb-2 px-3 py-2 rounded-pill fw-bold"
                                                        style={{ fontSize: '0.9rem' }}>
                                                        {telco}
                                                    </Badge>
                                                    <div className="text-muted small">Network Provider</div>
                                                </div>
                                            </div>
                                            <Button
                                                variant="outline-primary"
                                                size="sm"
                                                onClick={() => handleViewDetails(telco)}
                                                className="rounded-pill px-3 py-2 fw-semibold"
                                                style={{
                                                    transition: 'all 0.3s ease',
                                                    border: '2px solid #42a5f5'
                                                }}
                                            >
                                                <i className="bi bi-eye me-1"></i>
                                                View Details
                                            </Button>
                                        </Card.Header>
                                        <Card.Body className="p-4 position-relative">
                                            {/* Enhanced Transaction Stats */}
                                            <Row className="text-center mb-4 g-3">
                                                <Col xs={6}>
                                                    <div className="stats-mini-card p-3 rounded-3 h-100"
                                                        style={{
                                                            background: 'linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(30, 136, 229, 0.05) 100%)',
                                                            border: '1px solid rgba(66, 165, 245, 0.2)',
                                                            transition: 'all 0.3s ease'
                                                        }}>
                                                        <div className="icon-circle bg-primary bg-opacity-10 mx-auto mb-2 d-flex align-items-center justify-content-center"
                                                            style={{ width: '40px', height: '40px', borderRadius: '50%' }}>
                                                            <i className="bi bi-graph-up text-primary"></i>
                                                        </div>
                                                        <h4 className="text-primary mb-1 fw-bold">
                                                            {data.total_transactions.toLocaleString()}
                                                        </h4>
                                                        <small className="text-muted fw-semibold">Transactions</small>
                                                    </div>
                                                </Col>
                                                <Col xs={6}>
                                                    <div className="stats-mini-card p-3 rounded-3 h-100"
                                                        style={{
                                                            background: 'linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(25, 135, 84, 0.05) 100%)',
                                                            border: '1px solid rgba(40, 167, 69, 0.2)',
                                                            transition: 'all 0.3s ease'
                                                        }}>
                                                        <div className="icon-circle bg-success bg-opacity-10 mx-auto mb-2 d-flex align-items-center justify-content-center"
                                                            style={{ width: '40px', height: '40px', borderRadius: '50%' }}>
                                                            <i className="bi bi-currency-exchange text-success"></i>
                                                        </div>
                                                        <h4 className="text-success mb-1 fw-bold">
                                                            {formatCurrency(data.total_value_naira)}
                                                        </h4>
                                                        <small className="text-muted fw-semibold">Total Value</small>
                                                    </div>
                                                </Col>
                                            </Row>

                                            {/* Reconciliation Information */}
                                            {data.reconciliation && (
                                                <div className="mb-4">
                                                    <div className="d-flex align-items-center mb-3">
                                                        <div className="icon-circle bg-warning bg-opacity-10 me-2 d-flex align-items-center justify-content-center"
                                                            style={{ width: '24px', height: '24px', borderRadius: '50%' }}>
                                                            <i className="bi bi-arrow-left-right text-warning" style={{ fontSize: '0.8rem' }}></i>
                                                        </div>
                                                        <h6 className="mb-0 fw-bold text-dark">Reconciliation Analysis</h6>
                                                        <Badge
                                                            bg={data.reconciliation.match_rate >= 90 ? 'success' : data.reconciliation.match_rate >= 70 ? 'warning' : 'danger'}
                                                            className="ms-auto px-2 py-1 rounded-pill"
                                                        >
                                                            {data.reconciliation.match_rate}% Match
                                                        </Badge>
                                                    </div>

                                                    <Row className="g-2 mb-3">
                                                        <Col xs={4}>
                                                            <div className="text-center p-2 rounded-3"
                                                                style={{ background: 'rgba(13, 110, 253, 0.1)', border: '1px solid rgba(13, 110, 253, 0.2)' }}>
                                                                <div className="fw-bold text-primary small">{data.reconciliation.hisa_transactions}</div>
                                                                <div className="text-muted" style={{ fontSize: '0.7rem' }}>HISA</div>
                                                            </div>
                                                        </Col>
                                                        <Col xs={4}>
                                                            <div className="text-center p-2 rounded-3"
                                                                style={{ background: 'rgba(25, 135, 84, 0.1)', border: '1px solid rgba(25, 135, 84, 0.2)' }}>
                                                                <div className="fw-bold text-success small">{data.reconciliation.matching_transactions}</div>
                                                                <div className="text-muted" style={{ fontSize: '0.7rem' }}>Matched</div>
                                                            </div>
                                                        </Col>
                                                        <Col xs={4}>
                                                            <div className="text-center p-2 rounded-3"
                                                                style={{ background: 'rgba(220, 53, 69, 0.1)', border: '1px solid rgba(220, 53, 69, 0.2)' }}>
                                                                <div className="fw-bold text-danger small">{data.reconciliation.telco_transactions}</div>
                                                                <div className="text-muted" style={{ fontSize: '0.7rem' }}>Telco</div>
                                                            </div>
                                                        </Col>
                                                    </Row>

                                                    <div className="d-flex justify-content-between align-items-center p-2 rounded-3"
                                                        style={{ background: 'rgba(108, 117, 125, 0.1)', border: '1px solid rgba(108, 117, 125, 0.2)' }}>
                                                        <small className="text-muted">Value Difference:</small>
                                                        <small className="fw-bold text-dark">
                                                            {formatCurrency(data.reconciliation.value_difference_naira)}
                                                        </small>
                                                    </div>
                                                </div>
                                            )}

                                            {/* Enhanced Status Breakdown */}
                                            <div className="mb-4">
                                                <div className="d-flex align-items-center mb-3">
                                                    <div className="icon-circle bg-info bg-opacity-10 me-2 d-flex align-items-center justify-content-center"
                                                        style={{ width: '30px', height: '30px', borderRadius: '50%' }}>
                                                        <i className="bi bi-check-circle text-info small"></i>
                                                    </div>
                                                    <h6 className="mb-0 fw-bold text-muted">Transaction Status</h6>
                                                </div>
                                                <div className="status-breakdown">
                                                    {Object.entries(data.by_status).map(([status, statusData]) => (
                                                        <div key={status} className="status-item p-3 rounded-3 mb-2"
                                                            style={{
                                                                background: status.toLowerCase() === 'successful' ? 'rgba(40, 167, 69, 0.05)' :
                                                                    status.toLowerCase() === 'failed' ? 'rgba(220, 53, 69, 0.05)' :
                                                                        'rgba(255, 193, 7, 0.05)',
                                                                border: `1px solid ${status.toLowerCase() === 'successful' ? 'rgba(40, 167, 69, 0.2)' :
                                                                    status.toLowerCase() === 'failed' ? 'rgba(220, 53, 69, 0.2)' :
                                                                        'rgba(255, 193, 7, 0.2)'}`
                                                            }}>
                                                            <div className="d-flex justify-content-between align-items-center">
                                                                <div className="d-flex align-items-center">
                                                                    <Badge bg={getStatusColor(status)} className="me-2 px-2 py-1 rounded-pill">
                                                                        <i className={`bi ${status.toLowerCase() === 'successful' ? 'bi-check-circle' :
                                                                            status.toLowerCase() === 'failed' ? 'bi-x-circle' :
                                                                                'bi-clock'} me-1`}></i>
                                                                        {status}
                                                                    </Badge>
                                                                </div>
                                                                <div className="text-end">
                                                                    <div className="fw-bold fs-6">
                                                                        {statusData.count.toLocaleString()}
                                                                    </div>
                                                                    <small className="text-muted fw-medium">
                                                                        {formatCurrency(statusData.value_naira)}
                                                                    </small>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>

                                            {/* Enhanced Type Breakdown */}
                                            <div>
                                                <div className="d-flex align-items-center mb-3">
                                                    <div className="icon-circle bg-secondary bg-opacity-10 me-2 d-flex align-items-center justify-content-center"
                                                        style={{ width: '30px', height: '30px', borderRadius: '50%' }}>
                                                        <i className="bi bi-tags text-secondary small"></i>
                                                    </div>
                                                    <h6 className="mb-0 fw-bold text-muted">Transaction Types</h6>
                                                </div>
                                                <div className="type-breakdown">
                                                    {Object.entries(data.by_type).map(([type, typeData]) => (
                                                        <div key={type} className="type-item p-3 rounded-3 mb-2"
                                                            style={{
                                                                background: 'rgba(108, 117, 125, 0.05)',
                                                                border: '1px solid rgba(108, 117, 125, 0.2)'
                                                            }}>
                                                            <div className="d-flex justify-content-between align-items-center">
                                                                <div className="d-flex align-items-center">
                                                                    <Badge bg="secondary" className="me-2 px-2 py-1 rounded-pill">
                                                                        <i className="bi bi-tag me-1"></i>
                                                                        {type}
                                                                    </Badge>
                                                                </div>
                                                                <div className="text-end">
                                                                    <div className="fw-bold fs-6">
                                                                        {typeData.count.toLocaleString()}
                                                                    </div>
                                                                    <small className="text-muted fw-medium">
                                                                        {formatCurrency(typeData.value_naira)}
                                                                    </small>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        </Card.Body>
                                    </Card>
                                </Col>
                            ))}
                        </Row>

                        {/* Bill Transactions Section (HISA One only) */}
                        {hisaSource === 'hisa_one' && telcoSummary.bill_summary && Object.keys(telcoSummary.bill_summary).length > 0 && (
                            <>
                                {/* Bill Transactions Header */}
                                <Card className="shadow-lg border-0 rounded-4 mb-4 mt-4">
                                    <Card.Header
                                        className="text-white border-0 p-4"
                                        style={{
                                            background: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 50%, #1565c0 100%)',
                                            borderRadius: '1rem 1rem 0 0'
                                        }}
                                    >
                                        <h5 className="mb-0 fw-bold">
                                            <i className="bi bi-receipt me-2"></i>
                                            Bill Payment Transactions
                                        </h5>
                                        <p className="mb-0 mt-2 opacity-75 small">
                                            Bill payment transactions processed separately from telco transactions
                                        </p>
                                    </Card.Header>
                                </Card>

                                {/* Bill Transaction Cards */}
                                <Row>
                                    {Object.entries(telcoSummary.bill_summary).map(([billType, data]) => (
                                        <Col lg={4} md={6} key={billType} className="mb-4">
                                            <Card className="h-100 shadow-lg border-0 rounded-4 hover-card">
                                                <Card.Header
                                                    className="border-0 p-4"
                                                    style={{
                                                        background: 'linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 152, 0, 0.1) 50%, rgba(255, 111, 0, 0.1) 100%)',
                                                        borderRadius: '1rem 1rem 0 0'
                                                    }}
                                                >
                                                    <div className="d-flex align-items-center justify-content-between">
                                                        <div className="d-flex align-items-center">
                                                            <Badge bg="warning" className="me-2">{billType}</Badge>
                                                        </div>
                                                        <Badge bg="info" className="rounded-pill">
                                                            {data.category}
                                                        </Badge>
                                                    </div>
                                                </Card.Header>
                                                <Card.Body>
                                                    {/* Bill Transaction Stats */}
                                                    <Row className="text-center mb-3">
                                                        <Col xs={6}>
                                                            <div className="border-end">
                                                                <h4 className="text-primary mb-0">{data.total_transactions}</h4>
                                                                <small className="text-muted">Total Bills</small>
                                                            </div>
                                                        </Col>
                                                        <Col xs={6}>
                                                            <h4 className="text-success mb-0">₦{data.total_value_naira.toLocaleString()}</h4>
                                                            <small className="text-muted">Total Amount</small>
                                                        </Col>
                                                    </Row>

                                                    {/* Status Breakdown */}
                                                    <div className="mt-3">
                                                        <h6 className="text-muted mb-2">Status Breakdown:</h6>
                                                        {Object.entries(data.by_status).map(([status, statusData]) => (
                                                            <div key={status} className="d-flex justify-content-between align-items-center mb-2">
                                                                <Badge bg={status === 'SUCCESS' ? 'success' : 'danger'} className="me-2">
                                                                    {status}
                                                                </Badge>
                                                                <div className="text-end">
                                                                    <div className="fw-bold">{statusData.count} bills</div>
                                                                    <small className="text-muted">₦{statusData.value_naira.toLocaleString()}</small>
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </Card.Body>
                                            </Card>
                                        </Col>
                                    ))}
                                </Row>
                            </>
                        )}
                    </>
                )}



            </Container>

            <style>{`
                .hover-card {
                    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    overflow: hidden;
                }
                .hover-card:hover {
                    transform: translateY(-8px) scale(1.02);
                    box-shadow: 0 20px 40px rgba(66, 165, 245, 0.15), 0 8px 25px rgba(0,0,0,0.1) !important;
                }
                .hover-card::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                    transition: left 0.5s;
                    z-index: 1;
                }
                .hover-card:hover::before {
                    left: 100%;
                }
                .telco-card {
                    background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.9) 100%);
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(66, 165, 245, 0.1);
                }
                .stats-card {
                    transition: all 0.3s ease;
                    cursor: pointer;
                }
                .stats-card:hover {
                    transform: translateY(-3px);
                    box-shadow: 0 8px 25px rgba(66, 165, 245, 0.15);
                }
                .stats-mini-card {
                    transition: all 0.3s ease;
                    cursor: pointer;
                }
                .stats-mini-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 15px rgba(66, 165, 245, 0.1);
                }
                .status-item, .type-item {
                    transition: all 0.3s ease;
                    cursor: pointer;
                }
                .status-item:hover, .type-item:hover {
                    transform: translateX(5px);
                    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
                }
                .telco-icon-wrapper {
                    transition: all 0.3s ease;
                }
                .hover-card:hover .telco-icon-wrapper {
                    transform: rotate(10deg) scale(1.1);
                }
                .form-container {
                    min-height: 100vh;
                    padding: 2rem 0;
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                }
                .form-control-custom:focus {
                    border-color: #42a5f5 !important;
                    box-shadow: 0 0 0 0.25rem rgba(66, 165, 245, 0.15) !important;
                    transform: translateY(-1px);
                }
                .form-control-custom:hover:not(:disabled) {
                    border-color: #42a5f5;
                    transform: translateY(-1px);
                }
                .icon-circle {
                    transition: all 0.3s ease;
                }
                .hover-card:hover .icon-circle {
                    transform: scale(1.1);
                    box-shadow: 0 4px 15px rgba(66, 165, 245, 0.2);
                }
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }
                .stats-card:hover .icon-circle {
                    animation: pulse 2s infinite;
                }
                .page-transition {
                    animation: fadeInUp 0.6s ease-out;
                }
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                .btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 6px 20px rgba(66, 165, 245, 0.3);
                }
            `}</style>

            {/* User Consumption Modal */}
            <Modal show={showModal} onHide={() => setShowModal(false)} size="lg" centered>
                <Modal.Header closeButton>
                    <Modal.Title>
                        <i className="bi bi-people me-2"></i>
                        {selectedTelco} User Consumption Details
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    {modalLoading ? (
                        <div className="text-center py-4">
                            <Spinner animation="border" variant="primary" />
                            <p className="mt-2">Loading user consumption data...</p>
                        </div>
                    ) : userConsumption.length > 0 ? (
                        <div className="table-responsive">
                            <Table striped bordered hover>
                                <thead className="table-light">
                                    <tr>
                                        <th className="fw-semibold">Name</th>
                                        <th className="fw-semibold">Email</th>
                                        <th className="fw-semibold">User ID</th>
                                        <th className="fw-semibold text-end">Transactions</th>
                                        <th className="fw-semibold text-end">Total Amount</th>
                                        <th className="fw-semibold text-center">Success Rate</th>
                                        <th className="fw-semibold text-center">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {userConsumption.map((user, index) => (
                                        <tr key={index}>
                                            <td className="fw-medium">
                                                <div className="d-flex align-items-center">
                                                    <div className="bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2"
                                                        style={{ width: '32px', height: '32px' }}>
                                                        <i className="bi bi-person-fill text-primary"></i>
                                                    </div>
                                                    {user.name || 'Unknown User'}
                                                </div>
                                            </td>
                                            <td className="text-muted">
                                                <small>{user.email || 'No email available'}</small>
                                            </td>
                                            <td>
                                                <Badge bg="secondary" className="rounded-pill">{user.user_id}</Badge>
                                            </td>
                                            <td className="text-end">
                                                <div className="fw-bold">{user.total_transactions}</div>
                                                <small className="text-muted">
                                                    {user.successful_transactions || 0} success, {user.failed_transactions || 0} failed
                                                </small>
                                            </td>
                                            <td className="text-end">
                                                <div className="fw-bold text-success">₦{user.total_amount_naira?.toLocaleString() || '0'}</div>
                                            </td>
                                            <td className="text-center">
                                                <Badge bg={user.success_rate >= 90 ? 'success' : user.success_rate >= 70 ? 'warning' : 'danger'}
                                                    className="rounded-pill">
                                                    {user.success_rate?.toFixed(1) || '0'}%
                                                </Badge>
                                            </td>
                                            <td className="text-center">
                                                <Badge bg={user.success_rate >= 90 ? 'success' : user.success_rate >= 70 ? 'warning' : 'danger'}
                                                    className="rounded-pill">
                                                    {user.success_rate >= 90 ? 'Excellent' : user.success_rate >= 70 ? 'Good' : 'Poor'}
                                                </Badge>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </Table>
                        </div>
                    ) : (
                        <Alert variant="info" className="text-center">
                            <i className="bi bi-info-circle fs-1 d-block mb-3"></i>
                            <h5>No user consumption data available</h5>
                            <p>No consumption data found for {selectedTelco} on {targetDate}</p>
                        </Alert>
                    )}
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowModal(false)}>
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>
        </div >
    );
};

export default TelcoSummaryDashboard;