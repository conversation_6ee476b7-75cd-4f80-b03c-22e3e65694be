/**
 * Design System Styles
 * 
 * Comprehensive design system with modern CSS variables,
 * sophisticated animations, and responsive design patterns.
 */

// CSS Custom Properties (Design Tokens)
:root {
  // Color Palette
  --color-primary: #667eea;
  --color-primary-light: #764ba2;
  --color-primary-dark: #5a67d8;
  --color-secondary: #718096;
  --color-success: #48bb78;
  --color-danger: #f56565;
  --color-warning: #ed8936;
  --color-info: #4299e1;
  
  // Neutral Colors
  --color-white: #ffffff;
  --color-gray-50: #f7fafc;
  --color-gray-100: #edf2f7;
  --color-gray-200: #e2e8f0;
  --color-gray-300: #cbd5e0;
  --color-gray-400: #a0aec0;
  --color-gray-500: #718096;
  --color-gray-600: #4a5568;
  --color-gray-700: #2d3748;
  --color-gray-800: #1a202c;
  --color-gray-900: #171923;
  
  // Gradients
  --gradient-primary: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  --gradient-success: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  
  // Spacing
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  
  // Typography
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  // Shadows
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  // Border Radius
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  // Transitions
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  // Z-Index
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

// Enhanced Button Styles
.btn-enhanced {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-family-sans);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
  }
  
  &:hover::before {
    transform: translateX(100%);
  }
  
  // Sizes
  &--sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
    min-height: 2rem;
  }
  
  &--md {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-md);
    min-height: 2.5rem;
  }
  
  &--lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
    min-height: 3rem;
  }
  
  &--xl {
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: var(--font-size-xl);
    min-height: 3.5rem;
  }
  
  // Variants
  &--primary {
    background: var(--gradient-primary);
    color: var(--color-white);
    box-shadow: var(--shadow-md);
    
    &:hover {
      box-shadow: var(--shadow-lg);
      transform: translateY(-1px);
    }
  }
  
  &--glass {
    background: var(--gradient-glass);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    color: var(--color-gray-800);
    
    &:hover {
      background: rgba(255,255,255,0.2);
      border-color: rgba(255,255,255,0.3);
    }
  }
  
  &--minimal {
    background: transparent;
    color: var(--color-primary);
    
    &:hover {
      background: rgba(102, 126, 234, 0.1);
    }
  }
  
  // States
  &--loading {
    pointer-events: none;
    opacity: 0.7;
  }
  
  &--pulse {
    animation: pulse 2s infinite;
  }
  
  &--full-width {
    width: 100%;
  }
  
  &--rounded {
    border-radius: var(--radius-full);
  }
  
  &--elevated {
    box-shadow: var(--shadow-xl);
  }
  
  // Content
  &__content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }
  
  &__spinner {
    width: 1rem;
    height: 1rem;
  }
  
  &__icon {
    display: flex;
    align-items: center;
    
    &--left {
      margin-right: var(--spacing-xs);
    }
    
    &--right {
      margin-left: var(--spacing-xs);
    }
  }
}

// Enhanced Card Styles
.card-enhanced {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: all var(--transition-normal);
  
  &--default {
    border: 1px solid var(--color-gray-200);
    box-shadow: var(--shadow-sm);
  }
  
  &--elevated {
    box-shadow: var(--shadow-lg);
    border: none;
  }
  
  &--glass {
    background: var(--gradient-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255,255,255,0.2);
  }
  
  &--gradient {
    background: var(--gradient-primary);
    color: var(--color-white);
    border: none;
  }
  
  &--hoverable {
    cursor: pointer;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-xl);
    }
  }
  
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--color-gray-100);
    
    &-content {
      flex: 1;
    }
    
    &-actions {
      display: flex;
      gap: var(--spacing-sm);
    }
  }
  
  &__body {
    &--padding-none {
      padding: 0;
    }
    
    &--padding-sm {
      padding: var(--spacing-sm);
    }
    
    &--padding-md {
      padding: var(--spacing-lg);
    }
    
    &--padding-lg {
      padding: var(--spacing-xl);
    }
  }
  
  &__footer {
    padding: var(--spacing-lg);
    
    &--bordered {
      border-top: 1px solid var(--color-gray-100);
    }
  }
  
  // Loading state
  &__loading {
    padding: var(--spacing-lg);
  }
  
  &__skeleton {
    .skeleton-line {
      height: 1rem;
      background: linear-gradient(90deg, var(--color-gray-200) 25%, var(--color-gray-100) 50%, var(--color-gray-200) 75%);
      background-size: 200% 100%;
      animation: skeleton-loading 1.5s infinite;
      border-radius: var(--radius-sm);
      margin-bottom: var(--spacing-sm);
      
      &--title {
        height: 1.5rem;
        width: 60%;
      }
      
      &--text {
        width: 100%;
        
        &:last-of-type {
          width: 80%;
        }
      }
      
      &--button {
        height: 2.5rem;
        width: 120px;
        margin-top: var(--spacing-md);
      }
    }
  }
}

// Animations
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .btn-enhanced {
    &--lg {
      padding: var(--spacing-sm) var(--spacing-lg);
      font-size: var(--font-size-md);
      min-height: 2.5rem;
    }
    
    &--xl {
      padding: var(--spacing-md) var(--spacing-xl);
      font-size: var(--font-size-lg);
      min-height: 3rem;
    }
  }
  
  .card-enhanced {
    &__header,
    &__body--padding-md,
    &__footer {
      padding: var(--spacing-md);
    }
  }
}
