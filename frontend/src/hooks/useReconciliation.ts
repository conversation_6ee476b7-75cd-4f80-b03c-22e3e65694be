/**
 * Reconciliation Hook
 * 
 * Custom hook for managing reconciliation operations with
 * advanced state management and real-time updates.
 */

import { useState, useCallback, useRef } from 'react';
import { reconciliationService } from '../services/reconciliationService';
import { 
  ReconciliationRequest, 
  ReconciliationResult, 
  TelcoSummary,
  ProcessingStatus 
} from '../types/reconciliation';
import { useAuth } from './useAuth';
import { useNotification } from './useNotification';

interface UseReconciliationReturn {
  // State
  isProcessing: boolean;
  progress: number;
  status: ProcessingStatus;
  result: ReconciliationResult | null;
  error: string | null;
  
  // Actions
  processReconciliation: (request: ReconciliationRequest) => Promise<void>;
  getTelcoSummary: (targetDate: string, hisaSource: string, files?: any) => Promise<TelcoSummary>;
  cancelProcessing: () => void;
  clearResult: () => void;
  
  // History
  history: ReconciliationResult[];
  loadHistory: () => Promise<void>;
}

export const useReconciliation = (): UseReconciliationReturn => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [status, setStatus] = useState<ProcessingStatus>('idle');
  const [result, setResult] = useState<ReconciliationResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [history, setHistory] = useState<ReconciliationResult[]>([]);
  
  const { tokens } = useAuth();
  const { showNotification } = useNotification();
  const abortControllerRef = useRef<AbortController | null>(null);

  const processReconciliation = useCallback(async (request: ReconciliationRequest) => {
    if (!tokens) {
      throw new Error('Authentication required');
    }

    try {
      setIsProcessing(true);
      setProgress(0);
      setStatus('initializing');
      setError(null);
      
      // Create abort controller for cancellation
      abortControllerRef.current = new AbortController();
      
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + Math.random() * 10, 90));
      }, 500);

      setStatus('processing');
      
      const response = await reconciliationService.processReconciliation(
        request,
        tokens.access_token,
        abortControllerRef.current.signal
      );
      
      clearInterval(progressInterval);
      setProgress(100);
      setStatus('completed');
      setResult(response);
      
      showNotification({
        type: 'success',
        title: 'Reconciliation Complete',
        message: `Successfully processed ${request.telco} reconciliation for ${request.target_date}`
      });
      
    } catch (error: any) {
      setStatus('error');
      setError(error.message || 'Reconciliation failed');
      
      showNotification({
        type: 'error',
        title: 'Reconciliation Failed',
        message: error.message || 'An error occurred during reconciliation'
      });
      
      throw error;
    } finally {
      setIsProcessing(false);
      abortControllerRef.current = null;
    }
  }, [tokens, showNotification]);

  const getTelcoSummary = useCallback(async (
    targetDate: string, 
    hisaSource: string, 
    files?: any
  ): Promise<TelcoSummary> => {
    if (!tokens) {
      throw new Error('Authentication required');
    }

    try {
      setIsProcessing(true);
      setStatus('loading');
      setError(null);
      
      const summary = await reconciliationService.getTelcoSummary(
        targetDate,
        hisaSource,
        files,
        tokens.access_token
      );
      
      setStatus('completed');
      return summary;
      
    } catch (error: any) {
      setStatus('error');
      setError(error.message || 'Failed to load telco summary');
      throw error;
    } finally {
      setIsProcessing(false);
    }
  }, [tokens]);

  const cancelProcessing = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setIsProcessing(false);
      setStatus('cancelled');
      
      showNotification({
        type: 'info',
        title: 'Processing Cancelled',
        message: 'Reconciliation processing has been cancelled'
      });
    }
  }, [showNotification]);

  const clearResult = useCallback(() => {
    setResult(null);
    setError(null);
    setProgress(0);
    setStatus('idle');
  }, []);

  const loadHistory = useCallback(async () => {
    if (!tokens) {
      return;
    }

    try {
      const historyData = await reconciliationService.getHistory(tokens.access_token);
      setHistory(historyData);
    } catch (error: any) {
      console.error('Failed to load history:', error);
      showNotification({
        type: 'error',
        title: 'History Load Failed',
        message: 'Failed to load reconciliation history'
      });
    }
  }, [tokens, showNotification]);

  return {
    // State
    isProcessing,
    progress,
    status,
    result,
    error,
    
    // Actions
    processReconciliation,
    getTelcoSummary,
    cancelProcessing,
    clearResult,
    
    // History
    history,
    loadHistory
  };
};

// Hook for real-time reconciliation updates
export const useRealtimeReconciliation = (reconciliationId?: string) => {
  const [updates, setUpdates] = useState<any[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  
  // This would implement WebSocket connection for real-time updates
  // For now, it's a placeholder for the real-time functionality
  
  return {
    updates,
    isConnected
  };
};
