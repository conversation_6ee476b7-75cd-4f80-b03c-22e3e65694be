/**
 * Authentication Hook
 * 
 * Custom hook for managing authentication state and operations
 * following React best practices and modern patterns.
 */

import { useState, useEffect, useCallback, useContext, createContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { authService } from '../services/authService';
import { User, AuthTokens } from '../types/auth';

interface AuthContextType {
  user: User | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [tokens, setTokens] = useState<AuthTokens | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const storedTokens = localStorage.getItem('authTokens');
        if (storedTokens) {
          const parsedTokens: AuthTokens = JSON.parse(storedTokens);
          
          // Validate token
          const userData = await authService.validateToken(parsedTokens.access_token);
          if (userData) {
            setTokens(parsedTokens);
            setUser(userData);
          } else {
            // Token invalid, try refresh
            await refreshToken();
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        logout();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = useCallback(async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const response = await authService.login(email, password);
      
      setTokens(response.tokens);
      setUser(response.user);
      
      // Store tokens in localStorage
      localStorage.setItem('authTokens', JSON.stringify(response.tokens));
      
      navigate('/dashboard');
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [navigate]);

  const logout = useCallback(() => {
    setUser(null);
    setTokens(null);
    localStorage.removeItem('authTokens');
    navigate('/login');
  }, [navigate]);

  const refreshToken = useCallback(async () => {
    try {
      const storedTokens = localStorage.getItem('authTokens');
      if (!storedTokens) {
        throw new Error('No refresh token available');
      }

      const parsedTokens: AuthTokens = JSON.parse(storedTokens);
      const response = await authService.refreshToken(parsedTokens.refresh_token);
      
      setTokens(response.tokens);
      setUser(response.user);
      
      localStorage.setItem('authTokens', JSON.stringify(response.tokens));
    } catch (error) {
      console.error('Token refresh error:', error);
      logout();
    }
  }, [logout]);

  const updateUser = useCallback((userData: Partial<User>) => {
    setUser(prev => prev ? { ...prev, ...userData } : null);
  }, []);

  const value: AuthContextType = {
    user,
    tokens,
    isAuthenticated: !!user && !!tokens,
    isLoading,
    login,
    logout,
    refreshToken,
    updateUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Hook for protected routes
export const useRequireAuth = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, isLoading, navigate]);

  return { isAuthenticated, isLoading };
};

// Hook for admin-only access
export const useRequireAdmin = () => {
  const { user, isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        navigate('/login');
      } else if (!user?.is_admin) {
        navigate('/unauthorized');
      }
    }
  }, [user, isAuthenticated, isLoading, navigate]);

  return { isAuthorized: isAuthenticated && user?.is_admin, isLoading };
};
