/**
 * Enhanced Application Component
 * 
 * Modern React application with sophisticated routing, state management,
 * and user experience following best practices.
 */

import React, { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Toaster } from 'react-hot-toast';
import { ErrorBoundary } from 'react-error-boundary';

// Styles
import 'bootstrap/dist/css/bootstrap.min.css';
import './styles/design-system.scss';
import './styles/enhanced-app.scss';

// Providers and Hooks
import { AuthProvider } from './hooks/useAuth';
import { NotificationProvider } from './hooks/useNotification';

// Components
import { LoadingSpinner } from './components/ui/LoadingSpinner';
import { ErrorFallback } from './components/ui/ErrorFallback';
import { ProtectedRoute } from './components/auth/ProtectedRoute';

// Lazy-loaded pages for code splitting
const LoginPage = lazy(() => import('./pages/LoginPage'));
const DashboardPage = lazy(() => import('./pages/DashboardPage'));
const ReconciliationPage = lazy(() => import('./pages/ReconciliationPage'));
const TelcoSummaryPage = lazy(() => import('./pages/TelcoSummaryPage'));
const UserConsumptionPage = lazy(() => import('./pages/UserConsumptionPage'));
const SettingsPage = lazy(() => import('./pages/SettingsPage'));
const NotFoundPage = lazy(() => import('./pages/NotFoundPage'));

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

const App: React.FC = () => {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error, errorInfo) => {
        console.error('Application Error:', error, errorInfo);
        // Here you could send error to monitoring service
      }}
    >
      <QueryClientProvider client={queryClient}>
        <Router>
          <AuthProvider>
            <NotificationProvider>
              <div className="app-container">
                <Suspense fallback={<LoadingSpinner fullScreen />}>
                  <Routes>
                    {/* Public Routes */}
                    <Route path="/login" element={<LoginPage />} />
                    
                    {/* Protected Routes */}
                    <Route path="/" element={<ProtectedRoute />}>
                      <Route index element={<Navigate to="/dashboard" replace />} />
                      <Route path="dashboard" element={<DashboardPage />} />
                      <Route path="reconciliation" element={<ReconciliationPage />} />
                      <Route path="telco-summary" element={<TelcoSummaryPage />} />
                      <Route 
                        path="user-consumption/:targetDate/:telco/:hisaSource" 
                        element={<UserConsumptionPage />} 
                      />
                      <Route path="settings" element={<SettingsPage />} />
                    </Route>
                    
                    {/* Catch-all route */}
                    <Route path="*" element={<NotFoundPage />} />
                  </Routes>
                </Suspense>

                {/* Global Toast Notifications */}
                <Toaster
                  position="top-right"
                  toastOptions={{
                    duration: 4000,
                    style: {
                      background: '#363636',
                      color: '#fff',
                      borderRadius: '12px',
                      padding: '16px',
                      fontSize: '14px',
                      fontWeight: '500',
                    },
                    success: {
                      iconTheme: {
                        primary: '#48bb78',
                        secondary: '#fff',
                      },
                    },
                    error: {
                      iconTheme: {
                        primary: '#f56565',
                        secondary: '#fff',
                      },
                    },
                  }}
                />
              </div>
            </NotificationProvider>
          </AuthProvider>
        </Router>

        {/* React Query DevTools (only in development) */}
        {import.meta.env.DEV && (
          <ReactQueryDevtools initialIsOpen={false} />
        )}
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
