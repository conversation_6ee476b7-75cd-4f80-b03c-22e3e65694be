import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Form, Button, Alert, Modal, Card, Toast, ToastContainer } from 'react-bootstrap';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import 'bootstrap/dist/css/bootstrap.min.css';
import axios from 'axios';
import './App.css';
import ReconciliationResults from './components/ReconciliationResults';
import WalletsDashboard from './components/WalletsDashboard';
import CheckLogsDashboard from './components/CheckLogsDashboard';
import TelcoSummaryDashboard from './components/TelcoSummaryDashboard';
import UserConsumptionDetails from './components/UserConsumptionDetails';

// Use the existing VITE_API_BASE_URL environment variable
const baseURL = import.meta.env.DEV
  ? 'http://localhost:8080'
  : import.meta.env.VITE_API_BASE_URL;

// Import ReconciliationResult from types.ts
import { ReconciliationResult } from './types';

function AppContent() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [username, setUsername] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [authError, setAuthError] = useState<string>('');
  const [token, setToken] = useState<string>(() => {
    // Initialize token from localStorage if it exists
    return localStorage.getItem('authToken') || '';
  });

  const [selectedProvider, setSelectedProvider] = useState<string>('mtn');
  const [targetDate, setTargetDate] = useState<string>('');
  const [hisaFile, setHisaFile] = useState<File | null>(null);
  const [hisaFile2, setHisaFile2] = useState<File | null>(null);
  const [telcoFile, setTelcoFile] = useState<File | null>(null);
  const [telcoFile2, setTelcoFile2] = useState<File | null>(null);
  const [useTransactionId, setUseTransactionId] = useState<boolean>(false);
  const [result, setResult] = useState<ReconciliationResult | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [showErrorModal, setShowErrorModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [showSuccessToast, setShowSuccessToast] = useState<boolean>(false);

  // UI mode: 'choice' | 'reconcile-logs' | 'reconcile-wallets' | 'check-logs' | 'telco-summary'
  const [mode, setMode] = useState<'choice' | 'reconcile-logs' | 'reconcile-wallets' | 'check-logs' | 'telco-summary'>('choice');

  // Add Modal for displaying error messages
  const ErrorModal: React.FC = () => (
    <Modal show={showErrorModal} onHide={() => setShowErrorModal(false)}>
      <Modal.Header closeButton className="bg-danger text-white">
        <Modal.Title>
          <i className="bi bi-exclamation-triangle-fill me-2"></i>
          Form Validation Error
        </Modal.Title>
      </Modal.Header>
      <Modal.Body className="p-4">
        <div className="d-flex align-items-center mb-3">
          <div className="bg-danger text-white rounded-circle p-2 me-3">
            <i className="bi bi-x-lg"></i>
          </div>
          <div>
            <h5 className="mb-1">Please check your form</h5>
            <p className="text-muted mb-0">The following issues need to be resolved:</p>
          </div>
        </div>
        <div
          className="alert alert-danger"
          dangerouslySetInnerHTML={{ __html: errorMessage }}
        />
      </Modal.Body>
      <Modal.Footer>
        <Button variant="outline-secondary" onClick={() => setShowErrorModal(false)}>
          <i className="bi bi-x me-2"></i>
          Close
        </Button>
        <Button variant="danger" onClick={() => setShowErrorModal(false)}>
          <i className="bi bi-check2 me-2"></i>
          Fix Issues
        </Button>
      </Modal.Footer>
    </Modal>
  );

  // Success Toast component
  const SuccessToast: React.FC = () => (
    <ToastContainer position="top-end" className="p-3" style={{ zIndex: 1060 }}>
      <Toast
        show={showSuccessToast}
        onClose={() => setShowSuccessToast(false)}
        delay={5000}
        autohide
        bg="success"
      >
        <Toast.Header closeButton={false}>
          <i className="bi bi-check-circle-fill text-success me-2"></i>
          <strong className="me-auto">Success</strong>
          <button
            type="button"
            className="btn-close"
            onClick={() => setShowSuccessToast(false)}
            aria-label="Close"
          ></button>
        </Toast.Header>
        <Toast.Body className="text-white">
          <strong>All fields are valid!</strong> Processing your request...
        </Toast.Body>
      </Toast>
    </ToastContainer>
  );

  useEffect(() => {
    // Check if we have a token on component mount
    const storedToken = localStorage.getItem('authToken');
    if (storedToken) {
      setToken(storedToken);
      setIsAuthenticated(true);
      // Remove this line since we're not using a login modal
    }
  }, []);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setAuthError('');

    try {
      const formData = new FormData();
      formData.append('username', username);
      formData.append('password', password);

      const response = await axios.post(`${baseURL}/login/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const newToken = response.data.data.access_token;
      console.log('Login successful, token received:', newToken);
      setToken(newToken);
      localStorage.setItem('authToken', newToken);
      setIsAuthenticated(true);
      // Remove this line since we're not using a login modal
    } catch (err) {
      console.error('Login error:', err);
      setAuthError('Invalid credentials. Please try again.');
    }
  };

  const handleLogout = () => {
    setToken('');
    localStorage.removeItem('authToken');
    setIsAuthenticated(false);
    // Remove this line since we're not using a login modal
  };

  // Enhanced Choice screen after login
  const ChoiceScreen: React.FC = () => (
    <div className="form-container page-transition">
      <Container fluid className="px-4">
        <Card className="shadow-professional border-0 rounded-4 hover-card backdrop-blur">
          <Card.Header
            className="text-white p-5 rounded-top-4 border-0 d-flex justify-content-between align-items-center position-relative overflow-hidden"
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
            }}
          >
            {/* Animated background pattern */}
            <div
              className="position-absolute top-0 start-0 w-100 h-100 opacity-10"
              style={{
                backgroundImage: 'radial-gradient(circle at 20% 50%, white 2px, transparent 2px), radial-gradient(circle at 80% 50%, white 2px, transparent 2px)',
                backgroundSize: '60px 60px'
              }}
            />
            <div className="position-relative">
              <div className="d-flex align-items-center mb-3">
                <div className="icon-circle me-3">
                  <i className="bi bi-house-door-fill"></i>
                </div>
                <div>
                  <h2 className="mb-0 fw-bold text-gradient-white">
                    Welcome to HISA Reconciliation Manager
                  </h2>
                  <p className="mb-0 mt-2 opacity-90 fw-light fs-5">Choose a service to get started with professional reconciliation</p>
                </div>
              </div>
            </div>
            <Button
              variant="outline-light"
              onClick={handleLogout}
              className="px-4 py-2 rounded-pill fw-semibold transition-all position-relative"
              style={{ backdropFilter: 'blur(10px)' }}
            >
              <i className="bi bi-box-arrow-right me-2"></i>
              Logout
            </Button>
          </Card.Header>
          <Card.Body className="p-5">
            {/* Enhanced Main Services Grid */}
            <Row className="g-4 mb-5">
              <Col xl={3} lg={4} md={6} sm={12}>
                <Card className="h-100 shadow-professional border-0 rounded-4 hover-card transition-all position-relative overflow-hidden">
                  {/* Gradient overlay */}
                  <div
                    className="position-absolute top-0 start-0 w-100 h-100 opacity-5"
                    style={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                    }}
                  />
                  <Card.Body className="d-flex flex-column text-center p-4 position-relative">
                    <div className="mb-4">
                      <div className="icon-circle mx-auto mb-3 pulse-animation" style={{
                        background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1))',
                        border: '2px solid rgba(102, 126, 234, 0.2)'
                      }}>
                        <i className="bi bi-file-earmark-check-fill fs-1 text-gradient"></i>
                      </div>
                    </div>
                    <Card.Title className="h5 fw-bold mb-3" style={{ color: '#2d3748' }}>
                      Reconcile Reports
                    </Card.Title>
                    <Card.Text className="flex-grow-1 text-muted mb-4 lh-base">
                      Upload HISA and Telco files to generate comprehensive reconciliation reports with detailed analysis and insights.
                    </Card.Text>
                    <div className="mt-auto">
                      <Button
                        onClick={() => setMode('reconcile-logs')}
                        className="w-100 rounded-pill fw-semibold py-3 border-0 transition-all"
                        style={{
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)'
                        }}
                      >
                        <i className="bi bi-arrow-right-circle-fill me-2"></i>
                        Start Reconciliation
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>

              <Col xl={3} lg={4} md={6} sm={12}>
                <Card className="h-100 shadow-professional border-0 rounded-4 hover-card transition-all position-relative overflow-hidden">
                  <div
                    className="position-absolute top-0 start-0 w-100 h-100 opacity-5"
                    style={{
                      background: 'linear-gradient(135deg, #48bb78 0%, #38b2ac 100%)'
                    }}
                  />
                  <Card.Body className="d-flex flex-column text-center p-4 position-relative">
                    <div className="mb-4">
                      <div className="icon-circle mx-auto mb-3" style={{
                        background: 'linear-gradient(135deg, rgba(72, 187, 120, 0.1), rgba(56, 178, 172, 0.1))',
                        border: '2px solid rgba(72, 187, 120, 0.2)'
                      }}>
                        <i className="bi bi-wallet2-fill fs-1" style={{ color: '#48bb78' }}></i>
                      </div>
                    </div>
                    <Card.Title className="h5 fw-bold mb-3" style={{ color: '#2f855a' }}>
                      Reconcile Wallets
                    </Card.Title>
                    <Card.Text className="flex-grow-1 text-muted mb-4 lh-base">
                      View user balances, analyze wallet transactions, and reconcile individual user accounts with precision.
                    </Card.Text>
                    <div className="mt-auto">
                      <Button
                        onClick={() => setMode('reconcile-wallets')}
                        className="w-100 rounded-pill fw-semibold py-3 border-0 transition-all"
                        style={{
                          background: 'linear-gradient(135deg, #48bb78 0%, #38b2ac 100%)',
                          boxShadow: '0 8px 25px rgba(72, 187, 120, 0.3)',
                          color: 'white'
                        }}
                      >
                        <i className="bi bi-arrow-right-circle-fill me-2"></i>
                        Manage Wallets
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>

              <Col xl={3} lg={4} md={6} sm={12}>
                <Card className="h-100 shadow-professional border-0 rounded-4 hover-card transition-all position-relative overflow-hidden">
                  <div
                    className="position-absolute top-0 start-0 w-100 h-100 opacity-5"
                    style={{
                      background: 'linear-gradient(135deg, #ed8936 0%, #f6ad55 100%)'
                    }}
                  />
                  <Card.Body className="d-flex flex-column text-center p-4 position-relative">
                    <div className="mb-4">
                      <div className="icon-circle mx-auto mb-3" style={{
                        background: 'linear-gradient(135deg, rgba(237, 137, 54, 0.1), rgba(246, 173, 85, 0.1))',
                        border: '2px solid rgba(237, 137, 54, 0.2)'
                      }}>
                        <i className="bi bi-search-heart-fill fs-1" style={{ color: '#ed8936' }}></i>
                      </div>
                    </div>
                    <Card.Title className="h5 fw-bold mb-3" style={{ color: '#c05621' }}>
                      Check Logs
                    </Card.Title>
                    <Card.Text className="flex-grow-1 text-muted mb-4 lh-base">
                      Download and analyze user transaction logs with comprehensive balance reconciliation and insights.
                    </Card.Text>
                    <div className="mt-auto">
                      <Button
                        onClick={() => setMode('check-logs')}
                        className="w-100 rounded-pill fw-semibold py-3 border-0 transition-all"
                        style={{
                          background: 'linear-gradient(135deg, #ed8936 0%, #f6ad55 100%)',
                          boxShadow: '0 8px 25px rgba(237, 137, 54, 0.3)',
                          color: 'white'
                        }}
                      >
                        <i className="bi bi-arrow-right-circle-fill me-2"></i>
                        Analyze Logs
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>

              <Col xl={3} lg={4} md={6} sm={12}>
                <Card className="h-100 shadow-professional border-0 rounded-4 hover-card transition-all position-relative overflow-hidden">
                  <div
                    className="position-absolute top-0 start-0 w-100 h-100 opacity-5"
                    style={{
                      background: 'linear-gradient(135deg, #4299e1 0%, #63b3ed 100%)'
                    }}
                  />
                  <Card.Body className="d-flex flex-column text-center p-4 position-relative">
                    <div className="mb-4">
                      <div className="icon-circle mx-auto mb-3" style={{
                        background: 'linear-gradient(135deg, rgba(66, 153, 225, 0.1), rgba(99, 179, 237, 0.1))',
                        border: '2px solid rgba(66, 153, 225, 0.2)'
                      }}>
                        <i className="bi bi-bar-chart-line-fill fs-1" style={{ color: '#4299e1' }}></i>
                      </div>
                    </div>
                    <Card.Title className="h5 fw-bold mb-3" style={{ color: '#2b6cb0' }}>
                      Telco Summary
                    </Card.Title>
                    <Card.Text className="flex-grow-1 text-muted mb-4 lh-base">
                      View transaction summaries by telco and detailed user consumption analytics with visual reports.
                    </Card.Text>
                    <div className="mt-auto">
                      <Button
                        onClick={() => setMode('telco-summary')}
                        className="w-100 rounded-pill fw-semibold py-3 border-0 transition-all"
                        style={{
                          background: 'linear-gradient(135deg, #4299e1 0%, #63b3ed 100%)',
                          boxShadow: '0 8px 25px rgba(66, 153, 225, 0.3)',
                          color: 'white'
                        }}
                      >
                        <i className="bi bi-arrow-right-circle-fill me-2"></i>
                        View Analytics
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            </Row>

            {/* Enhanced Quick Stats */}
            <Row className="mt-5">
              <Col>
                <div
                  className="rounded-4 p-5 text-center position-relative overflow-hidden"
                  style={{
                    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.9))',
                    backdropFilter: 'blur(20px)',
                    border: '1px solid rgba(255, 255, 255, 0.3)'
                  }}
                >
                  <div
                    className="position-absolute top-0 start-0 w-100 h-100 opacity-5"
                    style={{
                      backgroundImage: 'radial-gradient(circle at 25% 25%, #667eea 2px, transparent 2px), radial-gradient(circle at 75% 75%, #764ba2 2px, transparent 2px)',
                      backgroundSize: '50px 50px'
                    }}
                  />
                  <Row className="g-4 position-relative">
                    <Col md={3} sm={6}>
                      <div className="d-flex align-items-center justify-content-center flex-column">
                        <div className="icon-circle mb-3" style={{
                          background: 'linear-gradient(135deg, rgba(72, 187, 120, 0.1), rgba(56, 178, 172, 0.1))',
                          border: '2px solid rgba(72, 187, 120, 0.2)'
                        }}>
                          <i className="bi bi-shield-check-fill fs-3" style={{ color: '#48bb78' }}></i>
                        </div>
                        <div className="fw-bold fs-5 mb-1" style={{ color: '#2f855a' }}>Secure</div>
                        <small className="text-muted fw-medium">End-to-end encryption</small>
                      </div>
                    </Col>
                    <Col md={3} sm={6}>
                      <div className="d-flex align-items-center justify-content-center flex-column">
                        <div className="icon-circle mb-3" style={{
                          background: 'linear-gradient(135deg, rgba(237, 137, 54, 0.1), rgba(246, 173, 85, 0.1))',
                          border: '2px solid rgba(237, 137, 54, 0.2)'
                        }}>
                          <i className="bi bi-lightning-charge-fill fs-3" style={{ color: '#ed8936' }}></i>
                        </div>
                        <div className="fw-bold fs-5 mb-1" style={{ color: '#c05621' }}>Fast</div>
                        <small className="text-muted fw-medium">Real-time processing</small>
                      </div>
                    </Col>
                    <Col md={3} sm={6}>
                      <div className="d-flex align-items-center justify-content-center flex-column">
                        <div className="icon-circle mb-3" style={{
                          background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1))',
                          border: '2px solid rgba(102, 126, 234, 0.2)'
                        }}>
                          <i className="bi bi-graph-up-arrow fs-3 text-gradient"></i>
                        </div>
                        <div className="fw-bold fs-5 mb-1 text-gradient">Accurate</div>
                        <small className="text-muted fw-medium">Precise reconciliation</small>
                      </div>
                    </Col>
                    <Col md={3} sm={6}>
                      <div className="d-flex align-items-center justify-content-center flex-column">
                        <div className="icon-circle mb-3" style={{
                          background: 'linear-gradient(135deg, rgba(66, 153, 225, 0.1), rgba(99, 179, 237, 0.1))',
                          border: '2px solid rgba(66, 153, 225, 0.2)'
                        }}>
                          <i className="bi bi-clock-fill fs-3" style={{ color: '#4299e1' }}></i>
                        </div>
                        <div className="fw-bold fs-5 mb-1" style={{ color: '#2b6cb0' }}>24/7</div>
                        <small className="text-muted fw-medium">Always available</small>
                      </div>
                    </Col>
                  </Row>
                </div>
              </Col>
            </Row>
          </Card.Body>
        </Card>
      </Container>

      <style>{`
        .hover-card {
          transition: all 0.3s ease;
          cursor: pointer;
        }
        .hover-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        }
        .icon-circle {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .form-container {
          min-height: 100vh;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 2rem 0;
        }
        @media (max-width: 768px) {
          .form-container {
            padding: 1rem 0;
          }
          .icon-circle {
            width: 60px;
            height: 60px;
          }
        }
      `}</style>
    </div>
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Detailed validation with specific error messages
    const missingFields = [];

    if (!targetDate) {
      missingFields.push("Target Date");
    }

    if (!hisaFile) {
      missingFields.push("Hisa1 File");
    }

    if (!hisaFile2) {
      missingFields.push("Hisa2 File");
    }

    if (!telcoFile) {
      missingFields.push("Telco File");
    }

    if (missingFields.length > 0) {
      const errorMsg = `
        <ul class="mb-0 ps-3">
          ${missingFields.map(field => `<li><strong>${field}</strong> is required</li>`).join('')}
        </ul>
      `;
      setErrorMessage(errorMsg);
      setShowErrorModal(true);
      return;
    }

    // Show success toast when all fields are valid
    setShowSuccessToast(true);

    setLoading(true);
    setErrorMessage('');

    const formData = new FormData();
    formData.append('mno', selectedProvider);
    formData.append('target_date', targetDate);
    if (hisaFile) formData.append('hisa_file', hisaFile);
    if (hisaFile2) formData.append('hisa_file2', hisaFile2);
    if (telcoFile) formData.append('telco_file', telcoFile);
    if (telcoFile2) {
      formData.append('telco_file2', telcoFile2);
    }
    formData.append('use_transaction_id', useTransactionId.toString());

    try {
      const response = await axios.post(
        `${baseURL}/reconcile_logs/`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${token}`
          },
        }
      );
      console.log('Reconciliation successful:', response.data);
      // Hide success toast when result is received
      setShowSuccessToast(false);
      setResult(response.data);
    } catch (err) {
      console.error('Reconciliation error:', err);
      // Hide success toast when an error occurs
      setShowSuccessToast(false);

      if (axios.isAxiosError(err)) {
        console.log('Error status:', err.response?.status);
        console.log('Error response:', err.response?.data);
      }

      if (axios.isAxiosError(err) && err.response?.status === 401) {
        handleLogout();
        setErrorMessage('Session expired. Please login again.');
        setShowErrorModal(true);
      } else if (axios.isAxiosError(err) && err.response?.status === 500) {
        const errorMsg = err.response.data.error || 'An unexpected error occurred';
        setErrorMessage(`
          <div class="d-flex align-items-center">
            <i class="bi bi-exclamation-circle-fill text-danger me-2 fs-5"></i>
            <strong>${errorMsg}</strong>
          </div>
        `);
        setShowErrorModal(true);
      } else {
        setErrorMessage(`
          <div class="d-flex align-items-center">
            <i class="bi bi-exclamation-circle-fill text-danger me-2 fs-5"></i>
            <strong>Error processing files. Please try again.</strong>
          </div>
          <div class="mt-2 text-muted">
            Check that your files are in the correct format and try again.
          </div>
        `);
        setShowErrorModal(true);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    setResult(null);
    setHisaFile(null);
    setHisaFile2(null);
    setTelcoFile(null);
    setTelcoFile2(null);
    setTargetDate('');
    setMode('choice');
  };

  useEffect(() => {
    console.log('Token changed:', token);
    console.log('Is authenticated:', isAuthenticated);
  }, [token, isAuthenticated]);

  if (!isAuthenticated) {
    return (
      <>
        <div className="form-container page-transition">
          <Container fluid className="px-4">
            <Row className="justify-content-center">
              <Col xl={5} lg={6} md={8} sm={10}>
                <Card className="shadow-professional border-0 rounded-4 backdrop-blur hover-card">
                  <Card.Header
                    className="text-white p-5 rounded-top-4 border-0 text-center position-relative overflow-hidden"
                    style={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                    }}
                  >
                    {/* Animated background pattern */}
                    <div
                      className="position-absolute top-0 start-0 w-100 h-100 opacity-10"
                      style={{
                        backgroundImage: 'radial-gradient(circle at 30% 30%, white 2px, transparent 2px), radial-gradient(circle at 70% 70%, white 2px, transparent 2px)',
                        backgroundSize: '40px 40px'
                      }}
                    />
                    <div className="position-relative">
                      <div className="mb-4">
                        <div className="icon-circle mx-auto mb-4 pulse-animation" style={{
                          background: 'rgba(255, 255, 255, 0.2)',
                          backdropFilter: 'blur(10px)',
                          border: '2px solid rgba(255, 255, 255, 0.3)'
                        }}>
                          <i className="bi bi-shield-lock-fill fs-1 text-white"></i>
                        </div>
                      </div>
                      <h2 className="mb-2 fw-bold">
                        Welcome Back
                      </h2>
                      <p className="mb-0 mt-2 opacity-90 fw-light fs-5">Sign in to HISA Reconciliation Manager</p>
                    </div>
                  </Card.Header>
                  <Card.Body className="p-5">
                    <Form onSubmit={handleLogin} className="slide-in">
                      <div className="mb-5">
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold mb-3 d-flex align-items-center" style={{ color: '#2d3748' }}>
                            <div className="me-3" style={{
                              width: '32px',
                              height: '32px',
                              borderRadius: '8px',
                              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}>
                              <i className="bi bi-person-fill text-white"></i>
                            </div>
                            Username
                          </Form.Label>
                          <Form.Control
                            type="text"
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                            required
                            className="form-control-custom focus-ring"
                            placeholder="Enter your username"
                            style={{
                              height: '4rem',
                              fontSize: '1.1rem',
                              background: 'rgba(255, 255, 255, 0.9)',
                              backdropFilter: 'blur(10px)'
                            }}
                          />
                        </Form.Group>

                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold mb-3 d-flex align-items-center" style={{ color: '#2d3748' }}>
                            <div className="me-3" style={{
                              width: '32px',
                              height: '32px',
                              borderRadius: '8px',
                              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}>
                              <i className="bi bi-lock-fill text-white"></i>
                            </div>
                            Password
                          </Form.Label>
                          <Form.Control
                            type="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            required
                            className="form-control-custom focus-ring"
                            placeholder="Enter your password"
                            style={{
                              height: '4rem',
                              fontSize: '1.1rem',
                              background: 'rgba(255, 255, 255, 0.9)',
                              backdropFilter: 'blur(10px)'
                            }}
                          />
                        </Form.Group>
                      </div>

                      {authError && (
                        <Alert
                          variant="danger"
                          className="mb-4 rounded-4 border-0 shadow-sm"
                          style={{
                            background: 'linear-gradient(135deg, rgba(245, 101, 101, 0.1), rgba(229, 62, 62, 0.1))',
                            backdropFilter: 'blur(10px)',
                            border: '1px solid rgba(245, 101, 101, 0.2)'
                          }}
                        >
                          <div className="d-flex align-items-center">
                            <div className="me-3" style={{
                              width: '32px',
                              height: '32px',
                              borderRadius: '8px',
                              background: 'linear-gradient(135deg, #f56565 0%, #e53e3e 100%)',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}>
                              <i className="bi bi-exclamation-triangle-fill text-white"></i>
                            </div>
                            <span className="fw-medium">{authError}</span>
                          </div>
                        </Alert>
                      )}

                      <div className="d-grid mt-5">
                        <Button
                          type="submit"
                          size="lg"
                          className="submit-button rounded-pill fw-bold border-0 position-relative overflow-hidden"
                          style={{
                            height: '4.5rem',
                            fontSize: '1.3rem',
                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            boxShadow: '0 12px 30px rgba(102, 126, 234, 0.4)',
                            textTransform: 'uppercase',
                            letterSpacing: '0.5px'
                          }}
                        >
                          <i className="bi bi-box-arrow-in-right-fill me-3"></i>
                          Sign In to Dashboard
                        </Button>
                      </div>
                    </Form>

                    {/* Enhanced Additional Info */}
                    <div className="text-center mt-5 pt-4" style={{
                      borderTop: '2px solid rgba(226, 232, 240, 0.6)'
                    }}>
                      <Row className="g-4">
                        <Col sm={4}>
                          <div className="d-flex align-items-center justify-content-center flex-column">
                            <div className="mb-2" style={{
                              width: '40px',
                              height: '40px',
                              borderRadius: '12px',
                              background: 'linear-gradient(135deg, rgba(72, 187, 120, 0.1), rgba(56, 178, 172, 0.1))',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              border: '1px solid rgba(72, 187, 120, 0.2)'
                            }}>
                              <i className="bi bi-shield-check-fill" style={{ color: '#48bb78' }}></i>
                            </div>
                            <small className="text-muted fw-medium">Secure Login</small>
                          </div>
                        </Col>
                        <Col sm={4}>
                          <div className="d-flex align-items-center justify-content-center flex-column">
                            <div className="mb-2" style={{
                              width: '40px',
                              height: '40px',
                              borderRadius: '12px',
                              background: 'linear-gradient(135deg, rgba(66, 153, 225, 0.1), rgba(99, 179, 237, 0.1))',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              border: '1px solid rgba(66, 153, 225, 0.2)'
                            }}>
                              <i className="bi bi-clock-fill" style={{ color: '#4299e1' }}></i>
                            </div>
                            <small className="text-muted fw-medium">24/7 Access</small>
                          </div>
                        </Col>
                        <Col sm={4}>
                          <div className="d-flex align-items-center justify-content-center flex-column">
                            <div className="mb-2" style={{
                              width: '40px',
                              height: '40px',
                              borderRadius: '12px',
                              background: 'linear-gradient(135deg, rgba(237, 137, 54, 0.1), rgba(246, 173, 85, 0.1))',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              border: '1px solid rgba(237, 137, 54, 0.2)'
                            }}>
                              <i className="bi bi-headset" style={{ color: '#ed8936' }}></i>
                            </div>
                            <small className="text-muted fw-medium">Support Ready</small>
                          </div>
                        </Col>
                      </Row>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          </Container>
        </div>

        <style>{`
          .icon-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .form-control-custom:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
          }
          .submit-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(13, 110, 253, 0.3);
          }
          @media (max-width: 768px) {
            .icon-circle {
              width: 60px;
              height: 60px;
            }
          }
        `}</style>
      </>
    );
  }

  if (result) {
    return (
      <>
        <div className="form-container">
          <ReconciliationResults result={result} onBack={handleBack} />
        </div>
      </>
    );
  }

  return (
    <>
      <ErrorModal />
      <SuccessToast />

      {mode === 'choice' && <ChoiceScreen />}

      {mode === 'reconcile-logs' && (
        <div className="form-container page-transition">
          <Container>
            <Card className="shadow-professional border-0 rounded-4 backdrop-blur hover-card">
              <Card.Header
                className="text-white p-5 rounded-top-4 border-0 d-flex justify-content-between align-items-center position-relative overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                }}
              >
                <div
                  className="position-absolute top-0 start-0 w-100 h-100 opacity-10"
                  style={{
                    backgroundImage: 'radial-gradient(circle at 20% 50%, white 2px, transparent 2px), radial-gradient(circle at 80% 50%, white 2px, transparent 2px)',
                    backgroundSize: '60px 60px'
                  }}
                />
                <div className="position-relative d-flex align-items-center">
                  <div className="icon-circle me-3" style={{
                    background: 'rgba(255, 255, 255, 0.2)',
                    backdropFilter: 'blur(10px)',
                    border: '2px solid rgba(255, 255, 255, 0.3)'
                  }}>
                    <i className="bi bi-file-earmark-check-fill"></i>
                  </div>
                  <div>
                    <h3 className="mb-0 fw-bold">HISA Logs Reconciliation</h3>
                    <p className="mb-0 mt-1 opacity-90 fw-light">Upload and reconcile transaction files</p>
                  </div>
                </div>
                <div className="d-flex gap-3 position-relative">
                  <Button
                    variant="outline-light"
                    onClick={() => setMode('choice')}
                    className="px-4 py-2 rounded-pill fw-semibold transition-all"
                    style={{ backdropFilter: 'blur(10px)' }}
                  >
                    <i className="bi bi-arrow-left me-2"></i>
                    Back
                  </Button>
                  <Button
                    variant="outline-light"
                    onClick={handleLogout}
                    className="px-4 py-2 rounded-pill fw-semibold transition-all"
                    style={{ backdropFilter: 'blur(10px)' }}
                  >
                    <i className="bi bi-box-arrow-right me-2"></i>
                    Logout
                  </Button>
                </div>
              </Card.Header>

              <Card.Body className="p-5">
                <Form onSubmit={handleSubmit} className="reconciliation-form slide-in">
                  {/* Enhanced Basic Information Section */}
                  <div className="form-section mb-5">
                    <h5 className="section-title mb-4">
                      <i className="bi bi-info-circle-fill"></i>
                      Basic Information
                    </h5>
                    <div className="input-group-custom">
                      <Form.Group className="mb-4">
                        <Form.Label className="fw-semibold mb-3 d-flex align-items-center" style={{ color: '#2d3748' }}>
                          <div className="me-3" style={{
                            width: '32px',
                            height: '32px',
                            borderRadius: '8px',
                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}>
                            <i className="bi bi-building text-white"></i>
                          </div>
                          Provider
                        </Form.Label>
                        <Form.Select
                          value={selectedProvider}
                          onChange={(e) => setSelectedProvider(e.target.value)}
                          required
                          className="form-select-custom focus-ring"
                        >
                          <option value="mtn">MTN Nigeria</option>
                          <option value="airtel">Airtel Nigeria</option>
                          <option value="glo">Globacom Limited</option>
                        </Form.Select>
                      </Form.Group>

                      <Form.Group className="mb-4">
                        <Form.Label className="fw-semibold mb-3 d-flex align-items-center" style={{ color: '#2d3748' }}>
                          <div className="me-3" style={{
                            width: '32px',
                            height: '32px',
                            borderRadius: '8px',
                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}>
                            <i className="bi bi-calendar-event text-white"></i>
                          </div>
                          Target Date
                        </Form.Label>
                        <Form.Control
                          type="date"
                          value={targetDate}
                          onChange={(e) => setTargetDate(e.target.value)}
                          required
                          className="form-control-custom focus-ring"
                        />
                      </Form.Group>
                    </div>
                  </div>

                  {/* Enhanced File Upload Section */}
                  <div className="form-section mb-5">
                    <h5 className="section-title mb-4">
                      <i className="bi bi-cloud-upload-fill"></i>
                      File Upload
                    </h5>
                    <Row className="g-4">
                      <Col md={6}>
                        <div className="file-upload-card">
                          <div className="file-upload-header mb-4">
                            <div className="d-flex align-items-center">
                              <div className="me-3" style={{
                                width: '40px',
                                height: '40px',
                                borderRadius: '12px',
                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}>
                                <i className="bi bi-file-earmark-text-fill text-white"></i>
                              </div>
                              <div>
                                <h6 className="mb-0 fw-bold" style={{ color: '#2d3748' }}>Hisa1 File</h6>
                                <small className="text-muted fw-medium">Required • CSV, Excel formats</small>
                              </div>
                            </div>
                          </div>
                          <div className="upload-box">
                            <Form.Control
                              type="file"
                              onChange={(e) => setHisaFile((e.target as HTMLInputElement).files?.[0] || null)}
                              required
                              className="d-none"
                              id="hisaFile"
                              accept=".csv,.xlsx,.xls"
                            />
                            <label htmlFor="hisaFile" className="upload-label w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                              <i className="bi bi-cloud-upload-fill upload-icon"></i>
                              <span className="upload-text">Click to upload or drag and drop</span>
                              <small className="text-muted mt-2">CSV, XLSX files up to 10MB</small>
                              {hisaFile && <span className="selected-file mt-3">{hisaFile.name}</span>}
                            </label>
                          </div>
                        </div>
                        <div className="file-upload-card mt-3">
                          <div className="file-upload-header mb-4">
                            <div className="d-flex align-items-center">
                              <div className="me-3" style={{
                                width: '40px',
                                height: '40px',
                                borderRadius: '12px',
                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}>
                                <i className="bi bi-file-earmark-text-fill text-white"></i>
                              </div>
                              <div>
                                <h6 className="mb-0 fw-bold" style={{ color: '#2d3748' }}>Hisa2 File</h6>
                                <small className="text-muted fw-medium">Required • CSV, Excel formats</small>
                              </div>
                            </div>
                          </div>
                          <div className="upload-box">
                            <Form.Control
                              type="file"
                              onChange={(e) => setHisaFile2((e.target as HTMLInputElement).files?.[0] || null)}
                              required
                              className="d-none"
                              id="hisaFile2"
                              accept=".csv,.xlsx,.xls"
                            />
                            <label htmlFor="hisaFile2" className="upload-label w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                              <i className="bi bi-cloud-upload-fill upload-icon"></i>
                              <span className="upload-text">Click to upload or drag and drop</span>
                              <small className="text-muted mt-2">CSV, XLSX files up to 10MB</small>
                              {hisaFile2 && <span className="selected-file mt-3">{hisaFile2.name}</span>}
                            </label>
                          </div>
                        </div>
                      </Col>
                      <Col md={6}>
                        <div className="file-upload-card">
                          <div className="file-upload-header mb-4">
                            <div className="d-flex align-items-center">
                              <div className="me-3" style={{
                                width: '40px',
                                height: '40px',
                                borderRadius: '12px',
                                background: 'linear-gradient(135deg, #48bb78 0%, #38b2ac 100%)',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}>
                                <i className="bi bi-phone-fill text-white"></i>
                              </div>
                              <div>
                                <h6 className="mb-0 fw-bold" style={{ color: '#2d3748' }}>Telco File</h6>
                                <small className="text-muted fw-medium">Required • CSV, Excel formats</small>
                              </div>
                            </div>
                          </div>
                          <div className="upload-box">
                            <Form.Control
                              type="file"
                              onChange={(e) => setTelcoFile((e.target as HTMLInputElement).files?.[0] || null)}
                              required
                              className="d-none"
                              id="telcoFile"
                              accept=".csv,.xlsx,.xls"
                            />
                            <label htmlFor="telcoFile" className="upload-label w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                              <i className="bi bi-cloud-upload-fill upload-icon"></i>
                              <span className="upload-text">Click to upload or drag and drop</span>
                              <small className="text-muted mt-2">CSV, XLSX files up to 10MB</small>
                              {telcoFile && <span className="selected-file mt-3">{telcoFile.name}</span>}
                            </label>
                          </div>
                        </div>
                        <div className="file-upload-card mt-3">
                          <div className="file-upload-header mb-4">
                            <div className="d-flex align-items-center">
                              <div className="me-3" style={{
                                width: '40px',
                                height: '40px',
                                borderRadius: '12px',
                                background: 'linear-gradient(135deg, #ed8936 0%, #f6ad55 100%)',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}>
                                <i className="bi bi-phone-plus-fill text-white"></i>
                              </div>
                              <div>
                                <h6 className="mb-0 fw-bold" style={{ color: '#2d3748' }}>Additional Telco File</h6>
                                <small className="text-muted fw-medium">Optional • CSV, Excel formats</small>
                              </div>
                            </div>
                          </div>
                          <div className="upload-box">
                            <Form.Control
                              type="file"
                              onChange={(e) => setTelcoFile2((e.target as HTMLInputElement).files?.[0] || null)}
                              className="d-none"
                              id="telcoFile2"
                              accept=".csv,.xlsx,.xls"
                            />
                            <label htmlFor="telcoFile2" className="upload-label w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                              <i className="bi bi-cloud-upload-fill upload-icon"></i>
                              <span className="upload-text">Click to upload or drag and drop</span>
                              <small className="text-muted mt-2">CSV, XLSX files up to 10MB</small>
                              {telcoFile2 && <span className="selected-file mt-3">{telcoFile2.name}</span>}
                            </label>
                          </div>
                        </div>
                      </Col>
                    </Row>
                  </div>

                  {/* Options Section */}
                  <div className="form-section mb-4">
                    <h5 className="section-title mb-4">Additional Options</h5>
                    <div className="options-card">
                      <Form.Check
                        type="checkbox"
                        id="transactionIdCheck"
                        checked={useTransactionId}
                        onChange={(e) => setUseTransactionId(e.target.checked)}
                        label="Use Transaction ID for matching"
                        className="custom-checkbox"
                      />
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="d-grid mt-5">
                    <Button
                      variant="primary"
                      type="submit"
                      disabled={loading}
                      size="lg"
                      className="submit-button"
                    >
                      {loading ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          Processing...
                        </>
                      ) : (
                        <>
                          <i className="bi bi-arrow-repeat me-2"></i>
                          Reconcile Transactions
                        </>
                      )}
                    </Button>
                  </div>
                </Form>
              </Card.Body>
            </Card>
          </Container>
        </div>
      )}

      {mode === 'reconcile-wallets' && (
        <div className="form-container page-transition">
          <Container>
            <Card className="shadow-professional border-0 rounded-4 backdrop-blur hover-card">
              <Card.Header
                className="text-white p-5 rounded-top-4 border-0 d-flex justify-content-between align-items-center position-relative overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, #48bb78 0%, #38b2ac 100%)'
                }}
              >
                <div
                  className="position-absolute top-0 start-0 w-100 h-100 opacity-10"
                  style={{
                    backgroundImage: 'radial-gradient(circle at 20% 50%, white 2px, transparent 2px), radial-gradient(circle at 80% 50%, white 2px, transparent 2px)',
                    backgroundSize: '60px 60px'
                  }}
                />
                <div className="position-relative d-flex align-items-center">
                  <div className="icon-circle me-3" style={{
                    background: 'rgba(255, 255, 255, 0.2)',
                    backdropFilter: 'blur(10px)',
                    border: '2px solid rgba(255, 255, 255, 0.3)'
                  }}>
                    <i className="bi bi-wallet2-fill"></i>
                  </div>
                  <div>
                    <h3 className="mb-0 fw-bold">HISA Wallets</h3>
                    <p className="mb-0 mt-1 opacity-90 fw-light">Manage and reconcile user wallets</p>
                  </div>
                </div>
                <div className="d-flex gap-3 position-relative">
                  <Button
                    variant="outline-light"
                    onClick={() => setMode('choice')}
                    className="px-4 py-2 rounded-pill fw-semibold transition-all"
                    style={{ backdropFilter: 'blur(10px)' }}
                  >
                    <i className="bi bi-arrow-left me-2"></i>
                    Back
                  </Button>
                  <Button
                    variant="outline-light"
                    onClick={handleLogout}
                    className="px-4 py-2 rounded-pill fw-semibold transition-all"
                    style={{ backdropFilter: 'blur(10px)' }}
                  >
                    <i className="bi bi-box-arrow-right me-2"></i>
                    Logout
                  </Button>
                </div>
              </Card.Header>
              <Card.Body className="p-4">
                <WalletsDashboard baseURL={baseURL} token={token} />
              </Card.Body>
            </Card>
          </Container>
        </div>
      )}

      {mode === 'check-logs' && (
        <div className="form-container page-transition">
          <Container>
            <Card className="shadow-professional border-0 rounded-4 backdrop-blur hover-card">
              <Card.Header
                className="text-white p-5 rounded-top-4 border-0 d-flex justify-content-between align-items-center position-relative overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, #ed8936 0%, #f6ad55 100%)'
                }}
              >
                <div
                  className="position-absolute top-0 start-0 w-100 h-100 opacity-10"
                  style={{
                    backgroundImage: 'radial-gradient(circle at 20% 50%, white 2px, transparent 2px), radial-gradient(circle at 80% 50%, white 2px, transparent 2px)',
                    backgroundSize: '60px 60px'
                  }}
                />
                <div className="position-relative d-flex align-items-center">
                  <div className="icon-circle me-3" style={{
                    background: 'rgba(255, 255, 255, 0.2)',
                    backdropFilter: 'blur(10px)',
                    border: '2px solid rgba(255, 255, 255, 0.3)'
                  }}>
                    <i className="bi bi-search-heart-fill"></i>
                  </div>
                  <div>
                    <h3 className="mb-0 fw-bold">Check User Logs</h3>
                    <p className="mb-0 mt-1 opacity-90 fw-light">Analyze user transaction logs</p>
                  </div>
                </div>
                <div className="d-flex gap-3 position-relative">
                  <Button
                    variant="outline-light"
                    onClick={() => setMode('choice')}
                    className="px-4 py-2 rounded-pill fw-semibold transition-all"
                    style={{ backdropFilter: 'blur(10px)' }}
                  >
                    <i className="bi bi-arrow-left me-2"></i>
                    Back
                  </Button>
                  <Button
                    variant="outline-light"
                    onClick={handleLogout}
                    className="px-4 py-2 rounded-pill fw-semibold transition-all"
                    style={{ backdropFilter: 'blur(10px)' }}
                  >
                    <i className="bi bi-box-arrow-right me-2"></i>
                    Logout
                  </Button>
                </div>
              </Card.Header>
              <Card.Body className="p-4">
                <CheckLogsDashboard baseURL={baseURL} token={token} />
              </Card.Body>
            </Card>
          </Container>
        </div>
      )}

      {mode === 'telco-summary' && (
        <div className="form-container page-transition">
          <Container>
            <Card className="shadow-professional border-0 rounded-4 backdrop-blur hover-card">
              <Card.Header
                className="text-white p-5 rounded-top-4 border-0 d-flex justify-content-between align-items-center position-relative overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, #4299e1 0%, #63b3ed 100%)'
                }}
              >
                <div
                  className="position-absolute top-0 start-0 w-100 h-100 opacity-10"
                  style={{
                    backgroundImage: 'radial-gradient(circle at 20% 50%, white 2px, transparent 2px), radial-gradient(circle at 80% 50%, white 2px, transparent 2px)',
                    backgroundSize: '60px 60px'
                  }}
                />
                <div className="position-relative d-flex align-items-center">
                  <div className="icon-circle me-3" style={{
                    background: 'rgba(255, 255, 255, 0.2)',
                    backdropFilter: 'blur(10px)',
                    border: '2px solid rgba(255, 255, 255, 0.3)'
                  }}>
                    <i className="bi bi-bar-chart-line-fill"></i>
                  </div>
                  <div>
                    <h3 className="mb-0 fw-bold">Telco Transaction Summary</h3>
                    <p className="mb-0 mt-1 opacity-90 fw-light">View analytics and transaction summaries</p>
                  </div>
                </div>
                <div className="d-flex gap-3 position-relative">
                  <Button
                    variant="outline-light"
                    onClick={() => setMode('choice')}
                    className="px-4 py-2 rounded-pill fw-semibold transition-all"
                    style={{ backdropFilter: 'blur(10px)' }}
                  >
                    <i className="bi bi-arrow-left me-2"></i>
                    Back
                  </Button>
                  <Button
                    variant="outline-light"
                    onClick={handleLogout}
                    className="px-4 py-2 rounded-pill fw-semibold transition-all"
                    style={{ backdropFilter: 'blur(10px)' }}
                  >
                    <i className="bi bi-box-arrow-right me-2"></i>
                    Logout
                  </Button>
                </div>
              </Card.Header>
              <Card.Body className="p-4">
                <TelcoSummaryDashboard baseURL={baseURL} token={token} />
              </Card.Body>
            </Card>
          </Container>
        </div>
      )}
    </>
  );
}

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<AppContent />} />
        <Route path="/user-consumption/:targetDate/:telco/:hisaSource" element={<UserConsumptionDetailsWrapper />} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
  );
}

// Wrapper component to pass props to UserConsumptionDetails
function UserConsumptionDetailsWrapper() {
  const baseURL = import.meta.env.DEV
    ? 'http://localhost:8080'
    : import.meta.env.VITE_API_BASE_URL;

  const token = localStorage.getItem('authToken') || '';

  return <UserConsumptionDetails baseURL={baseURL} token={token} />;
}

export default App;
