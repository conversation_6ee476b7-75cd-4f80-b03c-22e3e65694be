/**
 * Authentication Service
 * 
 * Service layer for authentication operations with proper error handling,
 * token management, and API integration.
 */

import { apiClient } from './apiClient';
import { User, AuthTokens, LoginResponse } from '../types/auth';

class AuthService {
  private readonly baseUrl = '/api/v2/auth';

  /**
   * Authenticate user with email and password
   */
  async login(email: string, password: string): Promise<LoginResponse> {
    try {
      const formData = new FormData();
      formData.append('username', email);
      formData.append('password', password);

      const response = await apiClient.post<AuthTokens>(`${this.baseUrl}/login`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      // Validate token and get user data
      const userData = await this.validateToken(response.data.access_token);
      
      return {
        tokens: response.data,
        user: userData
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Login failed');
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshToken: string): Promise<LoginResponse> {
    try {
      const response = await apiClient.post<AuthTokens>(`${this.baseUrl}/refresh`, {
        refresh_token: refreshToken
      });

      // Validate new token and get user data
      const userData = await this.validateToken(response.data.access_token);
      
      return {
        tokens: response.data,
        user: userData
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Token refresh failed');
    }
  }

  /**
   * Validate JWT token and return user data
   */
  async validateToken(token: string): Promise<User | null> {
    try {
      const formData = new FormData();
      formData.append('token', token);

      const response = await apiClient.post(`${this.baseUrl}/validate`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.valid) {
        return response.data.user;
      }
      
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get current authenticated user
   */
  async getCurrentUser(token: string): Promise<User> {
    try {
      const response = await apiClient.get<{ user: User }>(`${this.baseUrl}/me`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return response.data.user;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to get user data');
    }
  }

  /**
   * Logout user (client-side cleanup)
   */
  async logout(): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/logout`);
    } catch (error) {
      // Logout errors are not critical
      console.warn('Logout request failed:', error);
    }
  }

  /**
   * Change user password
   */
  async changePassword(
    currentPassword: string, 
    newPassword: string, 
    token: string
  ): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/change-password`, {
        current_password: currentPassword,
        new_password: newPassword
      }, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Password change failed');
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/password-reset-request`, {
        email
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Password reset request failed');
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/password-reset`, {
        token,
        new_password: newPassword
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Password reset failed');
    }
  }

  /**
   * Check if token is expired
   */
  isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch (error) {
      return true;
    }
  }

  /**
   * Get token expiration time
   */
  getTokenExpiration(token: string): Date | null {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return new Date(payload.exp * 1000);
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if user has admin privileges
   */
  isAdmin(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.is_admin === true;
    } catch (error) {
      return false;
    }
  }
}

export const authService = new AuthService();
