/**
 * Dashboard Page Component
 * 
 * Main dashboard page with comprehensive overview,
 * real-time updates, and intuitive navigation.
 */

import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { motion } from 'framer-motion';

import { useAuth } from '../hooks/useAuth';
import { useReconciliation } from '../hooks/useReconciliation';
import { EnhancedDashboard } from '../components/dashboard/EnhancedDashboard';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const { loadHistory } = useReconciliation();

  useEffect(() => {
    // Load initial data
    loadHistory();
  }, [loadHistory]);

  const pageVariants = {
    initial: { opacity: 0, y: 20 },
    in: { opacity: 1, y: 0 },
    out: { opacity: 0, y: -20 }
  };

  const pageTransition = {
    type: "tween",
    ease: "anticipate",
    duration: 0.5
  };

  return (
    <>
      <Helmet>
        <title>Dashboard - HISA Reconciliation Manager</title>
        <meta name="description" content="Comprehensive reconciliation dashboard with real-time analytics and insights" />
      </Helmet>

      <motion.div
        initial="initial"
        animate="in"
        exit="out"
        variants={pageVariants}
        transition={pageTransition}
        className="dashboard-page"
      >
        <EnhancedDashboard />
      </motion.div>
    </>
  );
};

export default DashboardPage;
